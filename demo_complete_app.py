#!/usr/bin/env python3
"""Demo script to showcase the complete ebook indexer application."""

import sys
import tempfile
import shutil
from pathlib import Path
from datetime import datetime

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

from ebook_indexer.config.settings import AppConfig
from ebook_indexer.core.scanner import DirectoryScanner
from ebook_indexer.core.metadata_extractor import MetadataExtractor
from ebook_indexer.core.anomaly_detector import AnomalyDetector
from ebook_indexer.utils.file_utils import FileUtils
from ebook_indexer.utils.progress_tracker import ProgressTracker
from ebook_indexer.utils.logging_config import setup_logging
from ebook_indexer.database.models import FileInfo, StructureInfo


def create_demo_directory_structure():
    """Create a demo directory structure with various scenarios."""
    print("Creating demo directory structure...")

    temp_dir = tempfile.mkdtemp(prefix="ebook_demo_")
    temp_path = Path(temp_dir)

    # Good structure examples
    print("  Creating well-organized collections...")

    # Collection 1: Fiction
    fiction_dir = temp_path / "fiction-collection"
    fiction_dir.mkdir()

    # Book 1: Properly organized
    book1_dir = fiction_dir / "the-great-gatsby"
    book1_dir.mkdir()
    (book1_dir / "the-great-gatsby.pdf").write_text(
        "The Great Gatsby by F. Scott Fitzgerald\n" + "Content " * 100
    )

    # Book 2: Another properly organized book
    book2_dir = fiction_dir / "to-kill-a-mockingbird"
    book2_dir.mkdir()
    (book2_dir / "to-kill-a-mockingbird.epub").write_text(
        "To Kill a Mockingbird by Harper Lee\n" + "Content " * 150
    )

    # Collection 2: Technical books
    tech_dir = temp_path / "technical-books"
    tech_dir.mkdir()

    book3_dir = tech_dir / "python-programming"
    book3_dir.mkdir()
    (book3_dir / "python-programming-guide.pdf").write_text(
        "Python Programming Guide\n" + "Technical content " * 200
    )

    # Anomaly examples
    print("  Creating examples with anomalies...")

    # Collection 3: Has anomalies
    messy_dir = temp_path / "messy collection"  # Space in name (anomaly)
    messy_dir.mkdir()

    # Misplaced file (should be in subdirectory)
    (messy_dir / "misplaced-book.pdf").write_text("This book is misplaced\n" + "Content " * 50)

    # File with problematic characters
    (messy_dir / "bad_filename.epub").write_text("Bad filename example\n" + "Content " * 75)

    # Deep nesting (too many levels)
    deep_path = messy_dir / "book" / "chapter" / "section" / "subsection" / "deep"
    deep_path.mkdir(parents=True)
    (deep_path / "deeply-nested.txt").write_text("This file is too deeply nested\n" + "Content " * 30)

    # Collection 4: Mixed scenarios
    mixed_dir = temp_path / "mixed-collection"
    mixed_dir.mkdir()

    # Some good books
    good_book_dir = mixed_dir / "good-book"
    good_book_dir.mkdir()
    (good_book_dir / "good-book.pdf").write_text("Well organized book\n" + "Content " * 100)

    # Some misplaced files
    (mixed_dir / "another-misplaced.epub").write_text("Another misplaced book\n" + "Content " * 80)

    print(f"  Demo structure created at: {temp_dir}")
    print(f"  Total collections: 4")
    print(f"  Total books: 8 (with various organizational issues)")

    return temp_dir


def demo_file_utils():
    """Demonstrate file utility functions."""
    print("\n" + "=" * 60)
    print("DEMO: File Utilities")
    print("=" * 60)

    # Create a temporary test file
    temp_dir = tempfile.mkdtemp()
    test_file = Path(temp_dir) / "test-book.pdf"
    test_content = "This is a test PDF file content for demonstration."
    test_file.write_text(test_content)

    try:
        print(f"Test file: {test_file}")

        # File hash
        file_hash = FileUtils.calculate_file_hash(str(test_file))
        print(f"✓ File hash (SHA256): {file_hash[:16]}...")

        # File size
        file_size = FileUtils.get_file_size(str(test_file))
        formatted_size = FileUtils.format_file_size(file_size)
        print(f"✓ File size: {file_size} bytes ({formatted_size})")

        # File info
        file_info = FileUtils.get_file_info(str(test_file))
        print(f"✓ File extension: {file_info['file_extension']}")
        print(f"✓ Is supported: {file_info['is_supported']}")
        print(f"✓ MIME type: {file_info['mime_type']}")

        # Safe filename
        unsafe_name = "bad_file_name.pdf"
        safe_name = FileUtils.safe_filename(unsafe_name)
        print(f"✓ Safe filename: '{unsafe_name}' → '{safe_name}'")

    finally:
        shutil.rmtree(temp_dir)


def demo_directory_scanner(demo_dir):
    """Demonstrate directory scanning functionality."""
    print("\n" + "=" * 60)
    print("DEMO: Directory Scanner")
    print("=" * 60)

    scanner = DirectoryScanner(['.pdf', '.epub', '.txt'])

    print(f"Scanning directory: {demo_dir}")

    # Get scan summary
    summary = scanner.get_scan_summary([demo_dir])

    print(f"✓ Total files found: {summary.total_files}")
    print(f"✓ Supported files: {summary.supported_files}")
    print(f"✓ Unsupported files: {summary.unsupported_files}")
    print(f"✓ Directories scanned: {summary.directories_scanned}")
    print(f"✓ Scan time: {summary.scan_time_seconds:.2f} seconds")

    # Show some file discoveries
    print("\nFile discoveries (first 5):")
    discoveries = list(scanner.scan_directory(demo_dir))
    for i, discovery in enumerate(discoveries[:5]):
        print(f"  {i+1}. {discovery.filename}")
        print(f"     Path: {discovery.relative_path}")
        print(f"     Collection: {discovery.collection_name}")
        print(f"     Book dir: {discovery.book_directory}")
        print(f"     Nesting level: {discovery.nesting_level}")
        print()


def demo_metadata_extractor(demo_dir):
    """Demonstrate metadata extraction."""
    print("\n" + "=" * 60)
    print("DEMO: Metadata Extractor")
    print("=" * 60)

    extractor = MetadataExtractor()

    print(f"Supported formats: {extractor.get_supported_formats()}")

    # Find some files to extract metadata from
    scanner = DirectoryScanner(['.pdf', '.epub', '.txt'])
    discoveries = list(scanner.scan_directory(demo_dir))

    print("\nMetadata extraction examples:")
    for i, discovery in enumerate(discoveries[:3]):
        print(f"\n{i+1}. File: {discovery.filename}")
        try:
            metadata = extractor.extract_metadata(discovery.file_path)
            print(f"   Title: {metadata.get('title', 'N/A')}")
            print(f"   Author: {metadata.get('author', 'N/A')}")
            print(f"   Format: {metadata.get('format', 'N/A')}")
            print(f"   Pages: {metadata.get('pages', 'N/A')}")
        except Exception as e:
            print(f"   Error: {e}")


def demo_anomaly_detector(demo_dir):
    """Demonstrate anomaly detection."""
    print("\n" + "=" * 60)
    print("DEMO: Anomaly Detector")
    print("=" * 60)

    config = {
        'max_nesting_depth': 4,
        'enforce_naming_convention': True,
        'detect_misplaced_files': True,
        'severity_thresholds': {
            'wrong_level': 'medium',
            'deep_nesting': 'low',
            'missing_directory': 'high',
            'naming_violation': 'low'
        }
    }

    detector = AnomalyDetector(config)
    scanner = DirectoryScanner(['.pdf', '.epub', '.txt'])

    print("Analyzing files for anomalies...")

    total_anomalies = 0
    anomaly_types = {}

    for discovery in scanner.scan_directory(demo_dir):
        # Create file and structure info
        file_info = FileInfo(
            file_path=discovery.file_path,
            directory_path=discovery.directory_path,
            filename=discovery.filename,
            file_extension=discovery.file_extension,
            file_size=discovery.file_size,
            file_hash="demo-hash",
            last_modified=discovery.last_modified
        )

        structure_info = StructureInfo(
            expected_path=discovery.file_path,
            collection_name=discovery.collection_name,
            book_directory=discovery.book_directory,
            nesting_level=discovery.nesting_level,
            follows_convention=True
        )

        # Detect anomalies
        anomalies = detector.detect_anomalies(file_info, structure_info)

        if anomalies:
            total_anomalies += len(anomalies)
            print(f"\n📁 {discovery.filename}")
            for anomaly in anomalies:
                anomaly_type = anomaly.type.value
                if anomaly_type not in anomaly_types:
                    anomaly_types[anomaly_type] = 0
                anomaly_types[anomaly_type] += 1

                print(f"   ⚠️  {anomaly.type.value} ({anomaly.severity.value})")
                print(f"      {anomaly.description}")
                if anomaly.suggested_action:
                    print(f"      💡 {anomaly.suggested_action}")

    print(f"\n📊 Anomaly Summary:")
    print(f"   Total anomalies: {total_anomalies}")
    for anomaly_type, count in anomaly_types.items():
        print(f"   {anomaly_type}: {count}")


def demo_progress_tracker():
    """Demonstrate progress tracking."""
    print("\n" + "=" * 60)
    print("DEMO: Progress Tracker")
    print("=" * 60)

    import time

    # Simulate a processing task
    total_items = 20
    tracker = ProgressTracker(
        total_items=total_items,
        description="Processing demo files"
    )

    print("Simulating file processing with progress tracking...")

    tracker.start()

    for i in range(total_items):
        # Simulate processing time
        time.sleep(0.1)  # 100ms per item

        # Update progress
        tracker.update(
            successful_delta=1,
            current_item=f"file_{i+1}.pdf"
        )

        # Show progress every 5 items
        if (i + 1) % 5 == 0:
            print(f"  {tracker.format_progress_line()}")

    # Final summary
    summary = tracker.get_summary()
    print(f"\n✓ Processing completed!")
    print(f"  Total time: {summary['elapsed_time_seconds']:.2f} seconds")
    print(f"  Processing rate: {summary['processing_rate']:.2f} items/sec")


def demo_complete_workflow(demo_dir):
    """Demonstrate complete indexing workflow (without database)."""
    print("\n" + "=" * 60)
    print("DEMO: Complete Indexing Workflow")
    print("=" * 60)

    # Create configuration
    config = AppConfig(
        root_directories=[demo_dir],
        supported_extensions=['.pdf', '.epub', '.txt'],
        max_workers=1,
        batch_size=5,
        anomaly_detection={
            'max_nesting_depth': 4,
            'enforce_naming_convention': True,
            'detect_misplaced_files': True,
            'severity_thresholds': {
                'wrong_level': 'medium',
                'deep_nesting': 'low',
                'missing_directory': 'high'
            }
        }
    )

    print("Configuration:")
    print(f"  Root directories: {config.root_directories}")
    print(f"  Supported extensions: {config.supported_extensions}")
    print(f"  Anomaly detection: Enabled")

    # Initialize components
    scanner = DirectoryScanner(config.supported_extensions)
    metadata_extractor = MetadataExtractor()
    anomaly_detector = AnomalyDetector(config.anomaly_detection)

    print("\n🔍 Scanning and processing files...")

    total_files = 0
    successful_files = 0
    files_with_anomalies = 0
    total_anomalies = 0

    # Process each file
    for discovery in scanner.scan_directory(demo_dir):
        total_files += 1

        try:
            # Extract metadata
            metadata = metadata_extractor.extract_metadata(discovery.file_path)

            # Create file and structure info for anomaly detection
            file_info = FileInfo(
                file_path=discovery.file_path,
                directory_path=discovery.directory_path,
                filename=discovery.filename,
                file_extension=discovery.file_extension,
                file_size=discovery.file_size,
                file_hash=FileUtils.calculate_file_hash(discovery.file_path),
                last_modified=discovery.last_modified
            )

            structure_info = StructureInfo(
                expected_path=discovery.file_path,
                collection_name=discovery.collection_name,
                book_directory=discovery.book_directory,
                nesting_level=discovery.nesting_level,
                follows_convention=True
            )

            # Detect anomalies
            anomalies = anomaly_detector.detect_anomalies(file_info, structure_info)

            successful_files += 1
            if anomalies:
                files_with_anomalies += 1
                total_anomalies += len(anomalies)

            print(f"  ✓ {discovery.filename} - {len(anomalies)} anomalies")

        except Exception as e:
            print(f"  ✗ {discovery.filename} - Error: {e}")

    # Summary
    print(f"\n📊 Processing Summary:")
    print(f"  Total files: {total_files}")
    print(f"  Successfully processed: {successful_files}")
    print(f"  Files with anomalies: {files_with_anomalies}")
    print(f"  Total anomalies detected: {total_anomalies}")
    print(f"  Success rate: {(successful_files/total_files*100):.1f}%")


def main():
    """Run complete application demo."""
    print("🚀 Ebook Indexer - Complete Application Demo")
    print("=" * 60)

    # Setup logging
    setup_logging(log_level="INFO")

    # Create demo directory structure
    demo_dir = create_demo_directory_structure()

    try:
        # Run individual component demos
        demo_file_utils()
        demo_directory_scanner(demo_dir)
        demo_metadata_extractor(demo_dir)
        demo_anomaly_detector(demo_dir)
        demo_progress_tracker()
        demo_complete_workflow(demo_dir)

        print("\n" + "=" * 60)
        print("✅ DEMO COMPLETED SUCCESSFULLY!")
        print("=" * 60)
        print("\nAll components are working correctly:")
        print("  ✅ File utilities - Hash calculation, size detection, file info")
        print("  ✅ Directory scanner - Recursive scanning, structure analysis")
        print("  ✅ Metadata extractor - PDF/EPUB/basic metadata extraction")
        print("  ✅ Anomaly detector - Misplaced files, naming violations, deep nesting")
        print("  ✅ Progress tracker - Real-time progress monitoring")
        print("  ✅ Complete workflow - End-to-end processing")

        print(f"\n📁 Demo files created at: {demo_dir}")
        print("   (You can explore this directory to see the test structure)")

        print("\n🎯 Next Steps:")
        print("  1. Set up MongoDB for database functionality")
        print("  2. Run: ebook-indexer index --roots /path/to/your/ebooks")
        print("  3. Use CLI commands to view results and manage anomalies")

        return 0

    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
        return 1

    finally:
        # Clean up demo directory
        try:
            shutil.rmtree(demo_dir)
            print(f"\n🧹 Cleaned up demo directory: {demo_dir}")
        except Exception as e:
            print(f"\n⚠️  Could not clean up demo directory: {e}")


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
