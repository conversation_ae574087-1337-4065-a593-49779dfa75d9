"""Metadata extraction from ebook files."""

import re
from pathlib import Path
from typing import Dict, Any, Optional
from datetime import datetime

try:
    import PyPDF2
except ImportError:
    PyPDF2 = None

try:
    import ebooklib
    from ebooklib import epub
except ImportError:
    ebooklib = None
    epub = None

from ..utils.logging_config import LoggerMixin
from ..exceptions.custom_exceptions import MetadataExtractionError


class MetadataExtractor(LoggerMixin):
    """Extract metadata from various ebook formats."""
    
    def __init__(self):
        """Initialize metadata extractor."""
        self.supported_formats = []
        
        if PyPDF2:
            self.supported_formats.append('.pdf')
        if ebooklib:
            self.supported_formats.append('.epub')
        
        self.logger.info(f"Metadata extractor initialized for formats: {self.supported_formats}")
    
    def extract_metadata(self, file_path: str) -> Dict[str, Any]:
        """
        Extract metadata from ebook file.
        
        Args:
            file_path: Path to the ebook file
            
        Returns:
            Dictionary with extracted metadata
            
        Raises:
            MetadataExtractionError: If metadata extraction fails
        """
        file_path_obj = Path(file_path)
        
        if not file_path_obj.exists():
            raise MetadataExtractionError(f"File not found: {file_path}", file_path=file_path)
        
        file_ext = file_path_obj.suffix.lower()
        
        try:
            if file_ext == '.pdf':
                return self._extract_pdf_metadata(file_path)
            elif file_ext == '.epub':
                return self._extract_epub_metadata(file_path)
            else:
                # For unsupported formats, return basic metadata
                return self._extract_basic_metadata(file_path)
                
        except Exception as e:
            if isinstance(e, MetadataExtractionError):
                raise
            raise MetadataExtractionError(f"Error extracting metadata from {file_path}: {e}", file_path=file_path)
    
    def _extract_pdf_metadata(self, file_path: str) -> Dict[str, Any]:
        """Extract metadata from PDF file."""
        if not PyPDF2:
            raise MetadataExtractionError("PyPDF2 not available for PDF metadata extraction", file_path=file_path)
        
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                
                # Get document info
                doc_info = pdf_reader.metadata if pdf_reader.metadata else {}
                
                # Extract basic metadata
                metadata = {
                    'title': self._clean_metadata_value(doc_info.get('/Title')),
                    'author': self._clean_metadata_value(doc_info.get('/Author')),
                    'subject': self._clean_metadata_value(doc_info.get('/Subject')),
                    'creator': self._clean_metadata_value(doc_info.get('/Creator')),
                    'producer': self._clean_metadata_value(doc_info.get('/Producer')),
                    'creation_date': self._parse_pdf_date(doc_info.get('/CreationDate')),
                    'modification_date': self._parse_pdf_date(doc_info.get('/ModDate')),
                    'pages': len(pdf_reader.pages),
                    'format': 'PDF'
                }
                
                # Try to extract title from filename if not in metadata
                if not metadata['title']:
                    metadata['title'] = self._extract_title_from_filename(file_path)
                
                return self._normalize_metadata(metadata)
                
        except Exception as e:
            raise MetadataExtractionError(f"Error reading PDF metadata: {e}", file_path=file_path)
    
    def _extract_epub_metadata(self, file_path: str) -> Dict[str, Any]:
        """Extract metadata from EPUB file."""
        if not ebooklib:
            raise MetadataExtractionError("ebooklib not available for EPUB metadata extraction", file_path=file_path)
        
        try:
            book = epub.read_epub(file_path)
            
            metadata = {
                'title': book.get_metadata('DC', 'title'),
                'author': book.get_metadata('DC', 'creator'),
                'publisher': book.get_metadata('DC', 'publisher'),
                'publication_date': book.get_metadata('DC', 'date'),
                'language': book.get_metadata('DC', 'language'),
                'description': book.get_metadata('DC', 'description'),
                'isbn': book.get_metadata('DC', 'identifier'),
                'format': 'EPUB'
            }
            
            # EPUB metadata comes as lists, extract first value
            for key, value in metadata.items():
                if isinstance(value, list) and value:
                    metadata[key] = value[0][0] if isinstance(value[0], tuple) else value[0]
                elif not value:
                    metadata[key] = None
            
            # Try to extract title from filename if not in metadata
            if not metadata['title']:
                metadata['title'] = self._extract_title_from_filename(file_path)
            
            # Parse publication date
            if metadata['publication_date']:
                metadata['publication_date'] = self._parse_epub_date(metadata['publication_date'])
            
            return self._normalize_metadata(metadata)
            
        except Exception as e:
            raise MetadataExtractionError(f"Error reading EPUB metadata: {e}", file_path=file_path)
    
    def _extract_basic_metadata(self, file_path: str) -> Dict[str, Any]:
        """Extract basic metadata for unsupported formats."""
        file_path_obj = Path(file_path)
        
        metadata = {
            'title': self._extract_title_from_filename(file_path),
            'author': None,
            'publisher': None,
            'publication_date': None,
            'language': None,
            'description': None,
            'isbn': None,
            'pages': None,
            'format': file_path_obj.suffix.upper().lstrip('.')
        }
        
        return self._normalize_metadata(metadata)
    
    def _extract_title_from_filename(self, file_path: str) -> str:
        """Extract title from filename."""
        filename = Path(file_path).stem
        
        # Remove common patterns
        patterns_to_remove = [
            r'\[.*?\]',  # Remove text in brackets
            r'\(.*?\)',  # Remove text in parentheses
            r'_+',       # Replace underscores with spaces
            r'-+',       # Replace dashes with spaces
            r'\.+',      # Replace dots with spaces
        ]
        
        title = filename
        for pattern in patterns_to_remove:
            title = re.sub(pattern, ' ', title)
        
        # Clean up whitespace
        title = ' '.join(title.split())
        
        # Capitalize words
        title = title.title()
        
        return title if title else filename
    
    def _clean_metadata_value(self, value: Any) -> Optional[str]:
        """Clean and normalize metadata value."""
        if value is None:
            return None
        
        # Convert to string and strip whitespace
        cleaned = str(value).strip()
        
        # Return None for empty strings
        return cleaned if cleaned else None
    
    def _parse_pdf_date(self, date_str: Optional[str]) -> Optional[datetime]:
        """Parse PDF date format."""
        if not date_str:
            return None
        
        try:
            # PDF date format: D:YYYYMMDDHHmmSSOHH'mm'
            # Remove D: prefix if present
            if date_str.startswith('D:'):
                date_str = date_str[2:]
            
            # Extract just the date part (YYYYMMDD)
            date_part = date_str[:8]
            if len(date_part) == 8:
                return datetime.strptime(date_part, '%Y%m%d')
            
        except (ValueError, IndexError):
            pass
        
        return None
    
    def _parse_epub_date(self, date_str: str) -> Optional[datetime]:
        """Parse EPUB date format."""
        if not date_str:
            return None
        
        # Try common date formats
        date_formats = [
            '%Y-%m-%d',
            '%Y-%m-%dT%H:%M:%S',
            '%Y-%m-%dT%H:%M:%SZ',
            '%Y',
            '%Y-%m'
        ]
        
        for fmt in date_formats:
            try:
                return datetime.strptime(date_str[:len(fmt)], fmt)
            except ValueError:
                continue
        
        return None
    
    def _normalize_metadata(self, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Normalize metadata to standard format."""
        normalized = {}
        
        # Standard field mapping
        field_mapping = {
            'title': 'title',
            'author': 'author',
            'creator': 'author',  # PDF creator -> author
            'publisher': 'publisher',
            'publication_date': 'publication_date',
            'creation_date': 'publication_date',  # PDF creation -> publication
            'language': 'language',
            'description': 'description',
            'subject': 'description',  # PDF subject -> description
            'isbn': 'isbn',
            'pages': 'pages',
            'format': 'format'
        }
        
        for source_key, target_key in field_mapping.items():
            if source_key in metadata and metadata[source_key] is not None:
                if target_key not in normalized or normalized[target_key] is None:
                    normalized[target_key] = metadata[source_key]
        
        # Ensure all expected fields are present
        expected_fields = ['title', 'author', 'publisher', 'publication_date', 
                          'language', 'description', 'isbn', 'pages']
        
        for field in expected_fields:
            if field not in normalized:
                normalized[field] = None
        
        return normalized
    
    def is_format_supported(self, file_extension: str) -> bool:
        """Check if file format is supported for metadata extraction."""
        return file_extension.lower() in self.supported_formats
    
    def get_supported_formats(self) -> list[str]:
        """Get list of supported formats."""
        return self.supported_formats.copy()
