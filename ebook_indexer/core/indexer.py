"""Main ebook indexing orchestrator."""

import time
from typing import Optional, List, Dict, Any, Generator
from datetime import datetime
from pathlib import Path

from ..config.settings import AppConfig
from ..database.models import (
    BookDocument, ProcessingJob, FileInfo, StructureInfo,
    BookMetadata, ProcessingInfo, ProcessingStatus, JobStatus
)
from ..database.repository import BookRepository, JobRepository, AnomalyRepository
from ..database.connection import get_connection
from .scanner import DirectoryScanner, FileDiscovery
from .metadata_extractor import MetadataExtractor
from .anomaly_detector import AnomalyDetector
from ..utils.file_utils import FileUtils
from ..utils.progress_tracker import ProgressTracker
from ..utils.logging_config import LoggerMixin
from ..exceptions.custom_exceptions import (
    IndexingError, DatabaseError, ScanningError, MetadataExtractionError
)


class EbookIndexer(LoggerMixin):
    """Main ebook indexing orchestrator with integrated anomaly detection."""

    def __init__(self, config: AppConfig):
        """
        Initialize ebook indexer.

        Args:
            config: Application configuration
        """
        self.config = config

        # Initialize components
        self.scanner = DirectoryScanner(config.supported_extensions)
        self.metadata_extractor = MetadataExtractor()
        self.anomaly_detector = AnomalyDetector(config.anomaly_detection)

        # Initialize repositories
        self.book_repository = BookRepository()
        self.job_repository = JobRepository()
        self.anomaly_repository = AnomalyRepository()

        # Progress tracking
        self.progress_tracker: Optional[ProgressTracker] = None

        self.logger.info("Ebook indexer initialized")

    def run_indexing(self,
                    root_directories: Optional[List[str]] = None,
                    job_id: Optional[str] = None,
                    resume: bool = True) -> str:
        """
        Run main indexing process with anomaly detection.

        Args:
            root_directories: List of root directories to scan
            job_id: Existing job ID to resume (if resume=True)
            resume: Whether to resume existing job or start new one

        Returns:
            Job ID of the indexing operation

        Raises:
            IndexingError: If indexing fails
        """
        # Use config directories if not provided
        if not root_directories:
            root_directories = self.config.root_directories

        if not root_directories:
            raise IndexingError("No root directories specified for indexing")

        # Validate directories
        for directory in root_directories:
            if not Path(directory).exists():
                raise IndexingError(f"Directory does not exist: {directory}")

        try:
            # Create or resume processing job
            job = self._create_or_resume_job(root_directories, job_id, resume)

            self.logger.info(f"Starting indexing job {job.id} for directories: {root_directories}")

            # Update job status to running
            job.status = JobStatus.RUNNING
            job.timing.start_time = datetime.utcnow()
            self.job_repository.save_job(job)

            # Get file count for progress tracking
            total_files = self._count_files(root_directories)
            self.progress_tracker = ProgressTracker(
                total_items=total_files,
                description=f"Indexing {len(root_directories)} directories",
                update_callback=self._progress_update_callback
            )

            # Process files
            processed_count = 0
            successful_count = 0
            failed_count = 0
            anomaly_count = 0

            try:
                for file_discovery in self.scanner.scan_multiple_directories(root_directories):
                    try:
                        # Process single file
                        book_doc, has_anomalies = self._process_file(file_discovery, job.id)

                        # Save to database
                        self.book_repository.save_book(book_doc)

                        # Update counters
                        processed_count += 1
                        successful_count += 1
                        if has_anomalies:
                            anomaly_count += 1

                        # Update progress
                        self.progress_tracker.update(
                            successful_delta=1,
                            current_item=file_discovery.filename
                        )

                        # Update job statistics periodically
                        if processed_count % self.config.batch_size == 0:
                            self._update_job_statistics(job, processed_count, successful_count,
                                                      failed_count, anomaly_count)

                    except Exception as e:
                        failed_count += 1
                        self.logger.error(f"Failed to process file {file_discovery.file_path}: {e}")

                        # Create error book document
                        error_book = self._create_error_book_document(file_discovery, str(e), job.id)
                        self.book_repository.save_book(error_book)

                        # Update progress
                        self.progress_tracker.update(failed_delta=1)

                # Complete the job
                self._complete_job(job, processed_count, successful_count, failed_count, anomaly_count)

            except Exception as e:
                # Fail the job
                self._fail_job(job, str(e))
                raise

            return str(job.id)

        except Exception as e:
            if isinstance(e, IndexingError):
                raise
            raise IndexingError(f"Indexing failed: {e}")

    def _create_or_resume_job(self,
                             root_directories: List[str],
                             job_id: Optional[str],
                             resume: bool) -> ProcessingJob:
        """Create new job or resume existing one."""
        if resume and job_id:
            # Try to resume existing job
            existing_job = self.job_repository.get_job_by_id(job_id)
            if existing_job and existing_job.status in [JobStatus.PENDING, JobStatus.RUNNING]:
                self.logger.info(f"Resuming job {job_id}")
                return existing_job
            elif existing_job:
                self.logger.warning(f"Cannot resume job {job_id} with status {existing_job.status}")

        # Create new job
        job = ProcessingJob(
            root_directories=root_directories,
            status=JobStatus.PENDING
        )

        # Clear the auto-generated ID so it gets treated as a new document
        job.id = None

        job_id = self.job_repository.save_job(job)

        # Retrieve the saved job to get the complete object with proper ID
        saved_job = self.job_repository.get_job_by_id(job_id)
        if not saved_job:
            raise IndexingError(f"Failed to retrieve saved job {job_id}")

        self.logger.info(f"Created new job {job_id}")
        return saved_job

    def _count_files(self, root_directories: List[str]) -> int:
        """Count total files to be processed."""
        total = 0
        for directory in root_directories:
            try:
                files = FileUtils.find_files_by_extension(
                    directory,
                    self.config.supported_extensions,
                    recursive=True
                )
                total += len(files)
            except Exception as e:
                self.logger.warning(f"Error counting files in {directory}: {e}")

        self.logger.info(f"Found {total} files to process")
        return total

    def _process_file(self, file_discovery: FileDiscovery, job_id: str) -> tuple[BookDocument, bool]:
        """
        Process a single file and return book document.

        Args:
            file_discovery: File discovery information
            job_id: Processing job ID

        Returns:
            Tuple of (BookDocument, has_anomalies)
        """
        start_time = time.time()

        # Create file info
        file_info = FileInfo(
            file_path=file_discovery.file_path,
            directory_path=file_discovery.directory_path,
            filename=file_discovery.filename,
            file_extension=file_discovery.file_extension,
            file_size=file_discovery.file_size,
            file_hash=FileUtils.calculate_file_hash(file_discovery.file_path),
            last_modified=file_discovery.last_modified
        )

        # Create structure info
        structure_info = StructureInfo(
            expected_path=file_discovery.file_path,  # For now, actual = expected
            collection_name=file_discovery.collection_name,
            book_directory=file_discovery.book_directory,
            nesting_level=file_discovery.nesting_level,
            follows_convention=True  # Will be determined by anomaly detection
        )

        # Extract metadata
        try:
            metadata_dict = self.metadata_extractor.extract_metadata(file_discovery.file_path)
            metadata = BookMetadata(**metadata_dict)
        except MetadataExtractionError as e:
            self.logger.warning(f"Metadata extraction failed for {file_discovery.filename}: {e}")
            # Create minimal metadata
            metadata = BookMetadata(
                title=file_discovery.filename,
                format=file_discovery.file_extension.upper().lstrip('.')
            )

        # Detect anomalies
        anomalies = self.anomaly_detector.detect_anomalies(file_info, structure_info)
        has_anomalies = len(anomalies) > 0

        # Update structure info based on anomalies
        structure_info.follows_convention = not has_anomalies

        # Create processing info
        processing_time_ms = int((time.time() - start_time) * 1000)
        processing_info = ProcessingInfo(
            status=ProcessingStatus.COMPLETED,
            last_processed_date=datetime.utcnow(),
            processing_time_ms=processing_time_ms,
            job_id=job_id
        )

        # Create book document
        book_doc = BookDocument(
            file_info=file_info,
            metadata=metadata,
            structure_info=structure_info,
            processing=processing_info,
            anomalies=anomalies
        )

        # Clear the auto-generated ID so it gets treated as a new document
        book_doc.id = None

        return book_doc, has_anomalies

    def _create_error_book_document(self,
                                   file_discovery: FileDiscovery,
                                   error_message: str,
                                   job_id: str) -> BookDocument:
        """Create book document for failed processing."""
        # Create minimal file info
        file_info = FileInfo(
            file_path=file_discovery.file_path,
            directory_path=file_discovery.directory_path,
            filename=file_discovery.filename,
            file_extension=file_discovery.file_extension,
            file_size=file_discovery.file_size,
            file_hash="",  # Skip hash calculation for failed files
            last_modified=file_discovery.last_modified
        )

        # Create minimal structure info
        structure_info = StructureInfo(
            expected_path=file_discovery.file_path,
            collection_name=file_discovery.collection_name,
            book_directory=file_discovery.book_directory,
            nesting_level=file_discovery.nesting_level,
            follows_convention=False
        )

        # Create minimal metadata
        metadata = BookMetadata(
            title=file_discovery.filename,
            format=file_discovery.file_extension.upper().lstrip('.')
        )

        # Create error processing info
        processing_info = ProcessingInfo(
            status=ProcessingStatus.ERROR,
            error_message=error_message,
            last_processed_date=datetime.utcnow(),
            job_id=job_id
        )

        book_doc = BookDocument(
            file_info=file_info,
            metadata=metadata,
            structure_info=structure_info,
            processing=processing_info,
            anomalies=[]
        )

        # Clear the auto-generated ID so it gets treated as a new document
        book_doc.id = None

        return book_doc

    def _update_job_statistics(self,
                              job: ProcessingJob,
                              processed: int,
                              successful: int,
                              failed: int,
                              anomalous: int) -> None:
        """Update job statistics."""
        job.statistics.processed_files = processed
        job.statistics.successful_files = successful
        job.statistics.failed_files = failed
        job.statistics.anomalous_files = anomalous

        # Calculate processing rate
        if job.timing.start_time:
            elapsed = (datetime.utcnow() - job.timing.start_time).total_seconds()
            if elapsed > 0:
                job.statistics.processing_rate_files_per_sec = processed / elapsed

        # Save updated job
        self.job_repository.save_job(job)

    def _complete_job(self,
                     job: ProcessingJob,
                     processed: int,
                     successful: int,
                     failed: int,
                     anomalous: int) -> None:
        """Mark job as completed and update final statistics."""
        job.status = JobStatus.COMPLETED
        job.timing.end_time = datetime.utcnow()

        # Update final statistics
        self._update_job_statistics(job, processed, successful, failed, anomalous)

        # Calculate duration
        if job.timing.start_time and job.timing.end_time:
            duration = job.timing.end_time - job.timing.start_time
            job.timing.duration_seconds = int(duration.total_seconds())

        self.job_repository.save_job(job)

        if self.progress_tracker:
            self.progress_tracker.finish()

        self.logger.info(
            f"Job {job.id} completed: {successful}/{processed} files successful, "
            f"{failed} failed, {anomalous} with anomalies"
        )

    def _fail_job(self, job: ProcessingJob, error_message: str) -> None:
        """Mark job as failed."""
        job.status = JobStatus.FAILED
        job.timing.end_time = datetime.utcnow()

        # Store error in resume info for debugging
        job.resume_info.checkpoint_data = {"error": error_message}

        self.job_repository.save_job(job)

        if self.progress_tracker:
            self.progress_tracker.finish()

        self.logger.error(f"Job {job.id} failed: {error_message}")

    def _progress_update_callback(self, stats) -> None:
        """Callback for progress updates."""
        # Log progress periodically
        if stats.processed_items % 50 == 0:  # Every 50 files
            self.logger.info(self.progress_tracker.format_progress_line())

    def retry_failed_files(self, job_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Retry failed files and recheck anomalies.

        Args:
            job_id: Specific job ID to retry, or None for all recent failures

        Returns:
            Summary of retry operation
        """
        self.logger.info(f"Starting retry operation for job {job_id or 'all recent'}")

        # Get retry candidates
        retry_candidates = self.book_repository.get_retry_candidates(job_id)

        if not retry_candidates:
            self.logger.info("No files found that need retry")
            return {"retried": 0, "successful": 0, "still_failed": 0}

        retried = 0
        successful = 0
        still_failed = 0

        for book_doc in retry_candidates:
            try:
                retried += 1

                # Re-process the file
                file_discovery = self._book_doc_to_file_discovery(book_doc)
                new_book_doc, _ = self._process_file(file_discovery, book_doc.processing.job_id)

                # Update retry count
                new_book_doc.processing.retry_count = book_doc.processing.retry_count + 1
                new_book_doc.id = book_doc.id  # Keep same ID for update

                # Save updated document
                self.book_repository.update_book(new_book_doc)

                if new_book_doc.processing.status == ProcessingStatus.COMPLETED:
                    successful += 1
                else:
                    still_failed += 1

            except Exception as e:
                still_failed += 1
                self.logger.error(f"Retry failed for {book_doc.file_info.file_path}: {e}")

                # Update error message and retry count
                book_doc.processing.error_message = str(e)
                book_doc.processing.retry_count += 1
                self.book_repository.update_book(book_doc)

        summary = {
            "retried": retried,
            "successful": successful,
            "still_failed": still_failed
        }

        self.logger.info(f"Retry completed: {summary}")
        return summary

    def _book_doc_to_file_discovery(self, book_doc: BookDocument) -> FileDiscovery:
        """Convert book document back to file discovery for reprocessing."""
        return FileDiscovery(
            file_path=book_doc.file_info.file_path,
            directory_path=book_doc.file_info.directory_path,
            filename=book_doc.file_info.filename,
            file_extension=book_doc.file_info.file_extension,
            file_size=book_doc.file_info.file_size,
            last_modified=book_doc.file_info.last_modified,
            is_supported=True,  # Assume supported if it was processed before
            relative_path=book_doc.file_info.file_path,  # Simplified
            nesting_level=book_doc.structure_info.nesting_level,
            collection_name=book_doc.structure_info.collection_name,
            book_directory=book_doc.structure_info.book_directory
        )

    def get_indexing_summary(self, job_id: str) -> Dict[str, Any]:
        """
        Get comprehensive summary of indexing job.

        Args:
            job_id: Job ID to summarize

        Returns:
            Comprehensive job summary
        """
        job = self.job_repository.get_job_by_id(job_id)
        if not job:
            raise IndexingError(f"Job {job_id} not found")

        # Get book statistics for this job
        book_stats = self.book_repository.get_books_by_job(job_id)

        # Get anomaly statistics
        anomaly_stats = self.anomaly_repository.get_anomalies_by_job(job_id)

        return {
            "job": job.model_dump(),
            "book_count": len(book_stats),
            "anomaly_count": len(anomaly_stats),
            "summary": {
                "total_files": job.statistics.total_files,
                "processed_files": job.statistics.processed_files,
                "successful_files": job.statistics.successful_files,
                "failed_files": job.statistics.failed_files,
                "anomalous_files": job.statistics.anomalous_files,
                "processing_rate": job.statistics.processing_rate_files_per_sec,
                "duration_seconds": job.timing.duration_seconds,
                "status": job.status.value
            }
        }
