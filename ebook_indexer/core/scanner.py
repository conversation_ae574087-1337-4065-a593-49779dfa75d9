"""Directory scanning for ebook files."""

import os
from pathlib import Path
from typing import List, Dict, Any, Optional, Generator, Set
from dataclasses import dataclass
from datetime import datetime

from ..utils.file_utils import FileUtils
from ..utils.logging_config import LoggerMixin
from ..exceptions.custom_exceptions import ScanningError, FileProcessingError


@dataclass
class ScanResult:
    """Result of directory scanning."""
    total_files: int = 0
    supported_files: int = 0
    unsupported_files: int = 0
    total_size: int = 0
    directories_scanned: int = 0
    errors: List[str] = None
    scan_time_seconds: float = 0.0
    
    def __post_init__(self):
        if self.errors is None:
            self.errors = []


@dataclass
class FileDiscovery:
    """Discovered file information."""
    file_path: str
    directory_path: str
    filename: str
    file_extension: str
    file_size: int
    last_modified: datetime
    is_supported: bool
    relative_path: str
    nesting_level: int
    collection_name: Optional[str] = None
    book_directory: Optional[str] = None


class DirectoryScanner(LoggerMixin):
    """Scanner for discovering ebook files in directory structures."""
    
    def __init__(self, supported_extensions: Optional[List[str]] = None):
        """
        Initialize directory scanner.
        
        Args:
            supported_extensions: List of supported file extensions
        """
        self.supported_extensions = supported_extensions or list(FileUtils.SUPPORTED_EXTENSIONS)
        self.logger.info(f"Scanner initialized with extensions: {self.supported_extensions}")
    
    def scan_directory(self, root_path: str, recursive: bool = True) -> Generator[FileDiscovery, None, None]:
        """
        Scan directory for ebook files.
        
        Args:
            root_path: Root directory to scan
            recursive: Whether to scan recursively
            
        Yields:
            FileDiscovery objects for each found file
            
        Raises:
            ScanningError: If directory cannot be scanned
        """
        try:
            root_path_obj = Path(root_path).resolve()
            
            if not root_path_obj.exists():
                raise ScanningError(f"Directory does not exist: {root_path}", directory_path=root_path)
            
            if not root_path_obj.is_dir():
                raise ScanningError(f"Path is not a directory: {root_path}", directory_path=root_path)
            
            self.logger.info(f"Starting scan of directory: {root_path_obj}")
            
            # Use os.walk for better performance and control
            for current_dir, subdirs, files in os.walk(root_path_obj):
                current_path = Path(current_dir)
                
                # Calculate relative path and nesting level
                try:
                    relative_path = current_path.relative_to(root_path_obj)
                    nesting_level = len(relative_path.parts)
                except ValueError:
                    # This shouldn't happen with os.walk, but just in case
                    relative_path = Path(".")
                    nesting_level = 0
                
                # Process files in current directory
                for filename in files:
                    file_path = current_path / filename
                    
                    try:
                        # Check if file extension is supported
                        file_ext = file_path.suffix.lower()
                        is_supported = file_ext in self.supported_extensions
                        
                        # Get basic file info
                        file_size = file_path.stat().st_size
                        last_modified = datetime.fromtimestamp(file_path.stat().st_mtime)
                        
                        # Analyze directory structure
                        collection_name, book_directory = self._analyze_directory_structure(
                            file_path, root_path_obj
                        )
                        
                        discovery = FileDiscovery(
                            file_path=str(file_path),
                            directory_path=str(current_path),
                            filename=filename,
                            file_extension=file_ext,
                            file_size=file_size,
                            last_modified=last_modified,
                            is_supported=is_supported,
                            relative_path=str(relative_path / filename),
                            nesting_level=nesting_level,
                            collection_name=collection_name,
                            book_directory=book_directory
                        )
                        
                        yield discovery
                        
                    except Exception as e:
                        self.logger.warning(f"Error processing file {file_path}: {e}")
                        continue
                
                # Control recursion
                if not recursive:
                    subdirs.clear()  # Don't descend into subdirectories
                    
        except Exception as e:
            if isinstance(e, ScanningError):
                raise
            raise ScanningError(f"Error scanning directory {root_path}: {e}", directory_path=root_path)
    
    def scan_multiple_directories(self, root_paths: List[str], recursive: bool = True) -> Generator[FileDiscovery, None, None]:
        """
        Scan multiple root directories.
        
        Args:
            root_paths: List of root directories to scan
            recursive: Whether to scan recursively
            
        Yields:
            FileDiscovery objects for each found file
        """
        for root_path in root_paths:
            self.logger.info(f"Scanning directory: {root_path}")
            try:
                yield from self.scan_directory(root_path, recursive)
            except ScanningError as e:
                self.logger.error(f"Failed to scan directory {root_path}: {e}")
                continue
    
    def get_scan_summary(self, root_paths: List[str], recursive: bool = True) -> ScanResult:
        """
        Get summary of directory scan without yielding individual files.
        
        Args:
            root_paths: List of root directories to scan
            recursive: Whether to scan recursively
            
        Returns:
            ScanResult with summary statistics
        """
        start_time = datetime.now()
        result = ScanResult()
        directories_seen: Set[str] = set()
        
        try:
            for discovery in self.scan_multiple_directories(root_paths, recursive):
                result.total_files += 1
                result.total_size += discovery.file_size
                directories_seen.add(discovery.directory_path)
                
                if discovery.is_supported:
                    result.supported_files += 1
                else:
                    result.unsupported_files += 1
            
            result.directories_scanned = len(directories_seen)
            
        except Exception as e:
            result.errors.append(f"Scan error: {e}")
            self.logger.error(f"Error during scan summary: {e}")
        
        end_time = datetime.now()
        result.scan_time_seconds = (end_time - start_time).total_seconds()
        
        self.logger.info(f"Scan completed: {result.total_files} files, {result.supported_files} supported")
        return result
    
    def _analyze_directory_structure(self, file_path: Path, root_path: Path) -> tuple[Optional[str], Optional[str]]:
        """
        Analyze directory structure to identify collection and book directory.
        
        Args:
            file_path: Path to the file
            root_path: Root scanning path
            
        Returns:
            Tuple of (collection_name, book_directory)
        """
        try:
            relative_path = file_path.relative_to(root_path)
            parts = relative_path.parts[:-1]  # Exclude filename
            
            if len(parts) == 0:
                # File is directly in root
                return None, None
            elif len(parts) == 1:
                # File is in a single subdirectory (could be collection or book)
                return parts[0], None
            elif len(parts) >= 2:
                # File is nested: assume first level is collection, second is book
                collection_name = parts[0]
                book_directory = parts[1]
                return collection_name, book_directory
            
        except ValueError:
            # File is not under root_path
            return None, None
        
        return None, None
    
    def find_duplicate_files(self, root_paths: List[str]) -> Dict[str, List[str]]:
        """
        Find duplicate files based on filename.
        
        Args:
            root_paths: List of root directories to scan
            
        Returns:
            Dictionary mapping filenames to list of file paths
        """
        filename_map: Dict[str, List[str]] = {}
        
        for discovery in self.scan_multiple_directories(root_paths):
            if discovery.is_supported:
                filename = discovery.filename.lower()
                if filename not in filename_map:
                    filename_map[filename] = []
                filename_map[filename].append(discovery.file_path)
        
        # Return only files that appear more than once
        duplicates = {name: paths for name, paths in filename_map.items() if len(paths) > 1}
        
        self.logger.info(f"Found {len(duplicates)} duplicate filenames")
        return duplicates
