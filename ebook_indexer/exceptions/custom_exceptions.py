"""Custom exception classes for ebook indexer."""

from typing import Optional, Any


class EbookIndexerError(Exception):
    """Base exception for all ebook indexer errors."""

    def __init__(self, message: str, details: Optional[Any] = None):
        super().__init__(message)
        self.message = message
        self.details = details


class ConfigurationError(EbookIndexerError):
    """Raised when there's an issue with configuration."""
    pass


class DatabaseError(EbookIndexerError):
    """Raised when there's a database-related error."""
    pass


class ConnectionError(DatabaseError):
    """Raised when database connection fails."""
    pass


class FileProcessingError(EbookIndexerError):
    """Raised when file processing fails."""

    def __init__(self, message: str, file_path: Optional[str] = None, details: Optional[Any] = None):
        super().__init__(message, details)
        self.file_path = file_path


class MetadataExtractionError(FileProcessingError):
    """Raised when metadata extraction fails."""
    pass


class ScanningError(EbookIndexerError):
    """Raised when directory scanning fails."""

    def __init__(self, message: str, directory_path: Optional[str] = None, details: Optional[Any] = None):
        super().__init__(message, details)
        self.directory_path = directory_path


class AnomalyDetectionError(EbookIndexerError):
    """Raised when anomaly detection fails."""
    pass


class IndexingError(EbookIndexerError):
    """Raised when indexing operation fails."""
    pass


class JobError(EbookIndexerError):
    """Raised when job processing fails."""

    def __init__(self, message: str, job_id: Optional[str] = None, details: Optional[Any] = None):
        super().__init__(message, details)
        self.job_id = job_id


class ValidationError(EbookIndexerError):
    """Raised when data validation fails."""
    pass


class RetryExhaustedError(EbookIndexerError):
    """Raised when retry attempts are exhausted."""

    def __init__(self, message: str, retry_count: int, details: Optional[Any] = None):
        super().__init__(message, details)
        self.retry_count = retry_count
