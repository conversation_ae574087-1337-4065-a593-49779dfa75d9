"""MongoDB repository implementation."""

from typing import List, Optional, Dict, Any
from pymongo.collection import Collection
from pymongo.database import Database
from pymongo import ASCENDING, DESCENDING
from bson import ObjectId

from .models import BookDocument, <PERSON>ingJob, AnomalyReport, JobStatus, ProcessingStatus
from .connection import get_connection
from ..utils.logging_config import LoggerMixin
from ..exceptions.custom_exceptions import DatabaseError, ValidationError


class BaseRepository(LoggerMixin):
    """Base repository class."""

    def __init__(self, collection_name: str):
        """
        Initialize repository.

        Args:
            collection_name: MongoDB collection name
        """
        self.collection_name = collection_name
        self._collection: Optional[Collection] = None

    @property
    def collection(self) -> Collection:
        """Get MongoDB collection."""
        if self._collection is None:
            connection = get_connection()
            database = connection.get_database()
            self._collection = database[self.collection_name]
        return self._collection

    def _handle_mongo_error(self, operation: str, error: Exception):
        """Handle MongoDB errors."""
        error_msg = f"MongoDB {operation} failed: {error}"
        self.logger.error(error_msg)
        raise DatabaseError(error_msg, details={"operation": operation, "collection": self.collection_name})

    def _convert_objectids_to_strings(self, doc: Dict[str, Any]) -> Dict[str, Any]:
        """Convert ObjectIds in document to strings for Pydantic compatibility."""
        if not doc:
            return doc

        # Convert main _id
        if doc.get("_id"):
            doc["_id"] = str(doc["_id"])

        # Convert job_id in processing info
        if doc.get("processing", {}).get("job_id"):
            doc["processing"]["job_id"] = str(doc["processing"]["job_id"])

        # Convert job_id in top level (for AnomalyReport)
        if doc.get("job_id"):
            doc["job_id"] = str(doc["job_id"])

        return doc

    def _convert_strings_to_objectids(self, doc: Dict[str, Any]) -> Dict[str, Any]:
        """Convert string IDs to ObjectIds for MongoDB storage."""
        if not doc:
            return doc

        # Convert main _id
        if doc.get("_id") and isinstance(doc["_id"], str):
            doc["_id"] = ObjectId(doc["_id"])

        # Convert job_id in processing info
        if doc.get("processing", {}).get("job_id") and isinstance(doc["processing"]["job_id"], str):
            doc["processing"]["job_id"] = ObjectId(doc["processing"]["job_id"])

        # Convert job_id in top level (for AnomalyReport)
        if doc.get("job_id") and isinstance(doc["job_id"], str):
            doc["job_id"] = ObjectId(doc["job_id"])

        return doc


class BookRepository(BaseRepository):
    """Repository for book documents."""

    def __init__(self):
        super().__init__("books")

    def save_book(self, book: BookDocument) -> str:
        """
        Save book document.

        Args:
            book: Book document to save

        Returns:
            Document ID as string

        Raises:
            DatabaseError: If save operation fails
        """
        try:
            # Convert to dict for MongoDB
            book_dict = book.model_dump(by_alias=True, exclude_unset=True)
            book_dict = self._convert_strings_to_objectids(book_dict)

            if book.id:
                # Update existing document
                result = self.collection.replace_one(
                    {"_id": book.id},
                    book_dict
                )
                if result.matched_count == 0:
                    raise DatabaseError(f"Book with ID {book.id} not found for update")
                doc_id = book.id
            else:
                # Insert new document
                result = self.collection.insert_one(book_dict)
                doc_id = result.inserted_id

            self.logger.debug(f"Saved book document with ID: {doc_id}")
            return str(doc_id)

        except Exception as e:
            self._handle_mongo_error("save_book", e)

    def get_book_by_id(self, book_id: str) -> Optional[BookDocument]:
        """
        Get book by ID.

        Args:
            book_id: Book document ID

        Returns:
            Book document or None if not found
        """
        try:
            doc = self.collection.find_one({"_id": ObjectId(book_id)})
            if doc:
                doc = self._convert_objectids_to_strings(doc)
                return BookDocument(**doc)
            return None

        except Exception as e:
            self._handle_mongo_error("get_book_by_id", e)

    def get_book_by_path(self, file_path: str) -> Optional[BookDocument]:
        """
        Get book by file path.

        Args:
            file_path: File path

        Returns:
            Book document or None if not found
        """
        try:
            doc = self.collection.find_one({"file_info.file_path": file_path})
            if doc:
                doc = self._convert_objectids_to_strings(doc)
                return BookDocument(**doc)
            return None

        except Exception as e:
            self._handle_mongo_error("get_book_by_path", e)

    def get_books_by_job(self, job_id: str, status: Optional[ProcessingStatus] = None) -> List[BookDocument]:
        """
        Get books by job ID.

        Args:
            job_id: Processing job ID
            status: Optional status filter

        Returns:
            List of book documents
        """
        try:
            query = {"processing.job_id": ObjectId(job_id)}
            if status:
                query["processing.status"] = status.value

            docs = self.collection.find(query)
            return [BookDocument(**self._convert_objectids_to_strings(doc)) for doc in docs]

        except Exception as e:
            self._handle_mongo_error("get_books_by_job", e)

    def get_retry_candidates(self, job_id: Optional[str] = None) -> List[BookDocument]:
        """
        Get books that need retry (failed or with unresolved anomalies).

        Args:
            job_id: Optional job ID filter

        Returns:
            List of book documents needing retry
        """
        try:
            query = {
                "$or": [
                    {"processing.status": ProcessingStatus.ERROR.value},
                    {
                        "anomalies": {
                            "$elemMatch": {
                                "resolved": False,
                                "severity": {"$in": ["medium", "high"]}
                            }
                        }
                    }
                ]
            }

            if job_id:
                query["processing.job_id"] = ObjectId(job_id)

            docs = self.collection.find(query)
            return [BookDocument(**self._convert_objectids_to_strings(doc)) for doc in docs]

        except Exception as e:
            self._handle_mongo_error("get_retry_candidates", e)

    def update_book(self, book: BookDocument) -> bool:
        """
        Update existing book document.

        Args:
            book: Book document to update

        Returns:
            True if updated successfully
        """
        if not book.id:
            raise ValidationError("Book ID is required for update")

        try:
            book_dict = book.model_dump(by_alias=True, exclude_unset=True)
            book_dict = self._convert_strings_to_objectids(book_dict)
            result = self.collection.replace_one(
                {"_id": ObjectId(book.id)},
                book_dict
            )

            success = result.matched_count > 0
            if success:
                self.logger.debug(f"Updated book document with ID: {book.id}")
            else:
                self.logger.warning(f"No book found with ID: {book.id}")

            return success

        except Exception as e:
            self._handle_mongo_error("update_book", e)

    def delete_book(self, book_id: str) -> bool:
        """
        Delete book document.

        Args:
            book_id: Book document ID

        Returns:
            True if deleted successfully
        """
        try:
            result = self.collection.delete_one({"_id": ObjectId(book_id)})
            success = result.deleted_count > 0

            if success:
                self.logger.debug(f"Deleted book document with ID: {book_id}")
            else:
                self.logger.warning(f"No book found with ID: {book_id}")

            return success

        except Exception as e:
            self._handle_mongo_error("delete_book", e)

    def get_all_books(self, limit: Optional[int] = None) -> List[BookDocument]:
        """
        Get all books.

        Args:
            limit: Optional limit on number of books to return

        Returns:
            List of all book documents
        """
        try:
            cursor = self.collection.find()
            if limit:
                cursor = cursor.limit(limit)

            return [BookDocument(**self._convert_objectids_to_strings(doc)) for doc in cursor]

        except Exception as e:
            self._handle_mongo_error("get_all_books", e)

    def count_books(self, query: Optional[Dict] = None) -> int:
        """
        Count books matching query.

        Args:
            query: Optional MongoDB query

        Returns:
            Number of matching documents
        """
        try:
            return self.collection.count_documents(query or {})
        except Exception as e:
            self._handle_mongo_error("count_books", e)


class JobRepository(BaseRepository):
    """Repository for processing jobs."""

    def __init__(self):
        super().__init__("processing_jobs")

    def save_job(self, job: ProcessingJob) -> str:
        """
        Save processing job.

        Args:
            job: Processing job to save

        Returns:
            Job ID as string
        """
        try:
            job_dict = job.model_dump(by_alias=True, exclude_unset=True)
            job_dict = self._convert_strings_to_objectids(job_dict)

            if job.id:
                # Update existing job
                result = self.collection.replace_one(
                    {"_id": ObjectId(job.id)},
                    job_dict
                )
                if result.matched_count == 0:
                    raise DatabaseError(f"Job with ID {job.id} not found for update")
                doc_id = job.id
            else:
                # Insert new job
                result = self.collection.insert_one(job_dict)
                doc_id = result.inserted_id

            self.logger.debug(f"Saved job document with ID: {doc_id}")
            return str(doc_id)

        except Exception as e:
            self._handle_mongo_error("save_job", e)

    def get_job_by_id(self, job_id: str) -> Optional[ProcessingJob]:
        """
        Get job by ID.

        Args:
            job_id: Job ID

        Returns:
            Processing job or None if not found
        """
        try:
            doc = self.collection.find_one({"_id": ObjectId(job_id)})
            if doc:
                doc = self._convert_objectids_to_strings(doc)
                return ProcessingJob(**doc)
            return None

        except Exception as e:
            self._handle_mongo_error("get_job_by_id", e)

    def get_recent_jobs(self, limit: int = 10) -> List[ProcessingJob]:
        """
        Get recent jobs.

        Args:
            limit: Maximum number of jobs to return

        Returns:
            List of recent processing jobs
        """
        try:
            docs = self.collection.find().sort("created_date", DESCENDING).limit(limit)
            return [ProcessingJob(**self._convert_objectids_to_strings(doc)) for doc in docs]

        except Exception as e:
            self._handle_mongo_error("get_recent_jobs", e)

    def get_active_jobs(self) -> List[ProcessingJob]:
        """
        Get active (running) jobs.

        Returns:
            List of active processing jobs
        """
        try:
            docs = self.collection.find({
                "status": {"$in": [JobStatus.PENDING.value, JobStatus.RUNNING.value]}
            })
            return [ProcessingJob(**self._convert_objectids_to_strings(doc)) for doc in docs]

        except Exception as e:
            self._handle_mongo_error("get_active_jobs", e)


class AnomalyRepository(BaseRepository):
    """Repository for anomaly reports."""

    def __init__(self):
        super().__init__("anomaly_reports")

    def save_anomaly(self, anomaly: AnomalyReport) -> str:
        """
        Save anomaly report.

        Args:
            anomaly: Anomaly report to save

        Returns:
            Anomaly ID as string
        """
        try:
            anomaly_dict = anomaly.model_dump(by_alias=True, exclude_unset=True)
            anomaly_dict = self._convert_strings_to_objectids(anomaly_dict)

            if anomaly.id:
                # Update existing anomaly
                result = self.collection.replace_one(
                    {"_id": anomaly.id},
                    anomaly_dict
                )
                if result.matched_count == 0:
                    raise DatabaseError(f"Anomaly with ID {anomaly.id} not found for update")
                doc_id = anomaly.id
            else:
                # Insert new anomaly
                result = self.collection.insert_one(anomaly_dict)
                doc_id = result.inserted_id

            self.logger.debug(f"Saved anomaly document with ID: {doc_id}")
            return str(doc_id)

        except Exception as e:
            self._handle_mongo_error("save_anomaly", e)

    def get_anomalies_by_job(self, job_id: str,
                           severity: Optional[List[str]] = None,
                           anomaly_types: Optional[List[str]] = None) -> List[Dict]:
        """
        Get anomalies by job with filtering.

        Args:
            job_id: Job ID
            severity: Optional severity filter
            anomaly_types: Optional anomaly type filter

        Returns:
            List of anomaly data
        """
        try:
            match_stage = {"job_id": ObjectId(job_id)}

            pipeline = [
                {"$match": match_stage}
            ]

            # Add severity filter
            if severity:
                pipeline.append({
                    "$match": {"severity": {"$in": severity}}
                })

            # Add type filter
            if anomaly_types:
                pipeline.append({
                    "$match": {"anomaly_type": {"$in": anomaly_types}}
                })

            # Project relevant fields
            pipeline.append({
                "$project": {
                    "file_path": 1,
                    "anomaly_type": 1,
                    "severity": 1,
                    "description": 1,
                    "context": 1,
                    "resolution": 1,
                    "detected_date": 1
                }
            })

            return list(self.collection.aggregate(pipeline))

        except Exception as e:
            self._handle_mongo_error("get_anomalies_by_job", e)


def create_database_indexes():
    """Create database indexes for optimal performance."""
    connection = get_connection()

    # Define indexes for each collection
    indexes = {
        "books": [
            # File path index (unique)
            {"keys": [("file_info.file_path", ASCENDING)], "unique": True},
            # Job ID index
            [("processing.job_id", ASCENDING)],
            # Status index
            [("processing.status", ASCENDING)],
            # Collection name index
            [("structure_info.collection_name", ASCENDING)],
            # File hash index (for duplicate detection)
            [("file_info.file_hash", ASCENDING)],
            # Compound index for retry candidates
            [("processing.status", ASCENDING), ("anomalies.resolved", ASCENDING)],
            # Created date index
            [("created_date", DESCENDING)]
        ],
        "processing_jobs": [
            # Status index
            [("status", ASCENDING)],
            # Created date index
            [("created_date", DESCENDING)],
            # Root directories index
            [("root_directories", ASCENDING)]
        ],
        "anomaly_reports": [
            # Job ID index
            [("job_id", ASCENDING)],
            # File path index
            [("file_path", ASCENDING)],
            # Anomaly type index
            [("anomaly_type", ASCENDING)],
            # Severity index
            [("severity", ASCENDING)],
            # Resolution status index
            [("resolution.status", ASCENDING)],
            # Compound index for filtering
            [("job_id", ASCENDING), ("severity", ASCENDING), ("anomaly_type", ASCENDING)],
            # Detected date index
            [("detected_date", DESCENDING)]
        ]
    }

    connection.create_indexes(indexes)
