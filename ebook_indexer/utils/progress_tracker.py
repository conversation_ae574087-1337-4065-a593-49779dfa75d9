"""Progress tracking utilities for ebook indexing operations."""

import time
from typing import Optional, Dict, Any, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, field

from .logging_config import LoggerMixin


@dataclass
class ProgressStats:
    """Progress statistics."""
    total_items: int = 0
    processed_items: int = 0
    successful_items: int = 0
    failed_items: int = 0
    skipped_items: int = 0
    start_time: Optional[datetime] = None
    current_time: Optional[datetime] = None

    @property
    def completion_percentage(self) -> float:
        """Calculate completion percentage."""
        if self.total_items == 0:
            return 0.0
        return (self.processed_items / self.total_items) * 100

    @property
    def elapsed_time(self) -> timedelta:
        """Calculate elapsed time."""
        if not self.start_time:
            return timedelta(0)
        end_time = self.current_time or datetime.utcnow()
        return end_time - self.start_time

    @property
    def processing_rate(self) -> float:
        """Calculate processing rate (items per second)."""
        elapsed = self.elapsed_time.total_seconds()
        if elapsed == 0:
            return 0.0
        return self.processed_items / elapsed

    @property
    def estimated_time_remaining(self) -> Optional[timedelta]:
        """Estimate time remaining."""
        if self.processing_rate == 0 or self.processed_items == 0:
            return None

        remaining_items = self.total_items - self.processed_items
        if remaining_items <= 0:
            return timedelta(0)

        seconds_remaining = remaining_items / self.processing_rate
        return timedelta(seconds=seconds_remaining)


class ProgressTracker(LoggerMixin):
    """Track progress of long-running operations with real-time updates."""

    def __init__(self,
                 total_items: int = 0,
                 description: str = "Processing",
                 update_callback: Optional[Callable[[ProgressStats], None]] = None,
                 update_interval: float = 1.0):
        """
        Initialize progress tracker.

        Args:
            total_items: Total number of items to process
            description: Description of the operation
            update_callback: Optional callback for progress updates
            update_interval: Minimum interval between updates (seconds)
        """
        self.stats = ProgressStats(total_items=total_items)
        self.description = description
        self.update_callback = update_callback
        self.update_interval = update_interval
        self.last_update_time = 0.0
        self.is_started = False
        self.is_finished = False

        self.logger.info(f"Progress tracker initialized: {description} ({total_items} items)")

    def start(self) -> None:
        """Start tracking progress."""
        if self.is_started:
            self.logger.warning("Progress tracker already started")
            return

        self.stats.start_time = datetime.utcnow()
        self.stats.current_time = self.stats.start_time
        self.is_started = True
        self.last_update_time = time.time()

        self.logger.info(f"Started: {self.description}")
        self._trigger_update(force=True)

    def update(self,
               processed_delta: int = 0,
               successful_delta: int = 0,
               failed_delta: int = 0,
               skipped_delta: int = 0,
               current_item: Optional[str] = None) -> None:
        """
        Update progress statistics.

        Args:
            processed_delta: Number of items processed since last update (if 0, calculated from other deltas)
            successful_delta: Number of successful items since last update
            failed_delta: Number of failed items since last update
            skipped_delta: Number of skipped items since last update
            current_item: Description of current item being processed
        """
        if not self.is_started:
            self.start()

        # Calculate processed_delta if not provided
        if processed_delta == 0:
            processed_delta = successful_delta + failed_delta + skipped_delta

        # Update statistics
        self.stats.processed_items += processed_delta
        self.stats.successful_items += successful_delta
        self.stats.failed_items += failed_delta
        self.stats.skipped_items += skipped_delta
        self.stats.current_time = datetime.utcnow()

        # Log current item if provided
        if current_item:
            self.logger.debug(f"Processing: {current_item}")

        # Trigger update if enough time has passed
        self._trigger_update()

        # Check if finished
        if self.stats.processed_items >= self.stats.total_items:
            self.finish()

    def set_total(self, total_items: int) -> None:
        """Update total number of items."""
        self.stats.total_items = total_items
        self.logger.debug(f"Updated total items to {total_items}")
        self._trigger_update(force=True)

    def increment_total(self, delta: int) -> None:
        """Increment total number of items."""
        self.stats.total_items += delta
        self.logger.debug(f"Incremented total items by {delta} to {self.stats.total_items}")

    def finish(self) -> None:
        """Mark progress as finished."""
        if self.is_finished:
            return

        self.is_finished = True
        self.stats.current_time = datetime.utcnow()

        # Log completion
        elapsed = self.stats.elapsed_time
        rate = self.stats.processing_rate

        self.logger.info(
            f"Completed: {self.description} - "
            f"{self.stats.processed_items}/{self.stats.total_items} items "
            f"({self.stats.completion_percentage:.1f}%) in {elapsed} "
            f"({rate:.2f} items/sec)"
        )

        # Final update
        self._trigger_update(force=True)

    def _trigger_update(self, force: bool = False) -> None:
        """Trigger progress update if conditions are met."""
        current_time = time.time()

        # Check if enough time has passed or force update
        if not force and (current_time - self.last_update_time) < self.update_interval:
            return

        self.last_update_time = current_time

        # Call update callback if provided
        if self.update_callback:
            try:
                self.update_callback(self.stats)
            except Exception as e:
                self.logger.error(f"Error in progress update callback: {e}")

    def get_stats(self) -> ProgressStats:
        """Get current progress statistics."""
        # Update current time
        if self.is_started and not self.is_finished:
            self.stats.current_time = datetime.utcnow()
        return self.stats

    def get_summary(self) -> Dict[str, Any]:
        """Get progress summary as dictionary."""
        stats = self.get_stats()

        return {
            'description': self.description,
            'total_items': stats.total_items,
            'processed_items': stats.processed_items,
            'successful_items': stats.successful_items,
            'failed_items': stats.failed_items,
            'skipped_items': stats.skipped_items,
            'completion_percentage': stats.completion_percentage,
            'elapsed_time_seconds': stats.elapsed_time.total_seconds(),
            'processing_rate': stats.processing_rate,
            'estimated_time_remaining_seconds': (
                stats.estimated_time_remaining.total_seconds()
                if stats.estimated_time_remaining else None
            ),
            'is_started': self.is_started,
            'is_finished': self.is_finished
        }

    def format_progress_line(self) -> str:
        """Format a single line progress string."""
        stats = self.get_stats()

        # Basic progress
        progress_str = f"{stats.processed_items}/{stats.total_items} ({stats.completion_percentage:.1f}%)"

        # Add rate if available
        if stats.processing_rate > 0:
            progress_str += f" - {stats.processing_rate:.2f} items/sec"

        # Add time remaining if available
        if stats.estimated_time_remaining:
            remaining = stats.estimated_time_remaining
            if remaining.total_seconds() < 3600:  # Less than 1 hour
                time_str = f"{remaining.seconds // 60}m {remaining.seconds % 60}s"
            else:
                hours = remaining.seconds // 3600
                minutes = (remaining.seconds % 3600) // 60
                time_str = f"{hours}h {minutes}m"
            progress_str += f" - ETA: {time_str}"

        return f"{self.description}: {progress_str}"


class BatchProgressTracker(ProgressTracker):
    """Progress tracker for batch operations with sub-progress tracking."""

    def __init__(self,
                 total_batches: int,
                 batch_size: int,
                 description: str = "Batch Processing",
                 update_callback: Optional[Callable[[ProgressStats], None]] = None):
        """
        Initialize batch progress tracker.

        Args:
            total_batches: Total number of batches
            batch_size: Size of each batch
            description: Description of the operation
            update_callback: Optional callback for progress updates
        """
        total_items = total_batches * batch_size
        super().__init__(total_items, description, update_callback)

        self.total_batches = total_batches
        self.batch_size = batch_size
        self.current_batch = 0
        self.current_batch_progress = 0

    def start_batch(self, batch_number: int) -> None:
        """Start processing a new batch."""
        self.current_batch = batch_number
        self.current_batch_progress = 0

        self.logger.debug(f"Starting batch {batch_number}/{self.total_batches}")

    def update_batch(self, items_processed: int = 1) -> None:
        """Update progress within current batch."""
        self.current_batch_progress += items_processed

        # Update overall progress
        overall_processed = (self.current_batch - 1) * self.batch_size + self.current_batch_progress
        delta = overall_processed - self.stats.processed_items

        if delta > 0:
            self.update(processed_delta=delta)

    def finish_batch(self) -> None:
        """Finish current batch."""
        # Ensure batch is fully counted
        expected_processed = self.current_batch * self.batch_size
        current_processed = self.stats.processed_items

        if expected_processed > current_processed:
            self.update(processed_delta=expected_processed - current_processed)

        self.logger.debug(f"Finished batch {self.current_batch}/{self.total_batches}")

    def get_batch_summary(self) -> Dict[str, Any]:
        """Get batch-specific progress summary."""
        summary = self.get_summary()
        summary.update({
            'total_batches': self.total_batches,
            'batch_size': self.batch_size,
            'current_batch': self.current_batch,
            'current_batch_progress': self.current_batch_progress,
            'batches_completed': self.current_batch - 1 if self.current_batch > 0 else 0
        })
        return summary
