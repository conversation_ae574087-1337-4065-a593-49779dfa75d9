"""Main CLI interface for ebook indexer."""

import sys
import json
from pathlib import Path
from typing import Optional, List

import click
from rich.console import Console
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn
from rich.panel import Panel
from rich.text import Text

from .config.settings import get_config, AppConfig
from .core.indexer import EbookIndexer
from .database.repository import BookRepository, JobRepository, AnomalyRepository
from .database.connection import get_connection
from .utils.logging_config import setup_logging
from .exceptions.custom_exceptions import IndexingError, DatabaseError


# Global console for rich output
console = Console()


@click.group()
@click.option('--config', '-c', type=click.Path(exists=True), help='Configuration file path')
@click.option('--verbose', '-v', is_flag=True, help='Enable verbose logging')
@click.pass_context
def cli(ctx, config: Optional[str], verbose: bool):
    """Ebook Indexer - Comprehensive ebook directory analysis and indexing tool."""
    # Ensure context object exists
    ctx.ensure_object(dict)

    # Load configuration
    try:
        app_config = get_config(config)
        ctx.obj['config'] = app_config

        # Setup logging
        log_level = "DEBUG" if verbose else app_config.log_level
        setup_logging(log_level=log_level, log_file=app_config.log_file)

        # Test database connection
        try:
            connection = get_connection()
            health = connection.health_check()
            if not health['connected']:
                console.print(f"[red]Database connection failed: {health['error']}[/red]")
                console.print("[yellow]Some commands may not work without database access[/yellow]")
                ctx.obj['db_available'] = False
            else:
                ctx.obj['db_available'] = True
        except Exception as e:
            console.print(f"[red]Database connection error: {e}[/red]")
            console.print("[yellow]Database commands will not be available[/yellow]")
            ctx.obj['db_available'] = False

    except Exception as e:
        console.print(f"[red]Configuration error: {e}[/red]")
        sys.exit(1)


@cli.command()
@click.option('--roots', '-r', multiple=True, help='Root directories to scan')
@click.option('--resume', is_flag=True, help='Resume interrupted indexing')
@click.option('--job-id', help='Job ID to resume')
@click.pass_context
def index(ctx, roots: tuple, resume: bool, job_id: Optional[str]):
    """Run ebook indexing with anomaly detection."""
    config: AppConfig = ctx.obj['config']

    # Use provided roots or config roots
    root_directories = list(roots) if roots else config.root_directories

    if not root_directories:
        console.print("[red]No root directories specified. Use --roots or configure in config file.[/red]")
        sys.exit(1)

    # Validate directories
    for directory in root_directories:
        if not Path(directory).exists():
            console.print(f"[red]Directory does not exist: {directory}[/red]")
            sys.exit(1)

    console.print(Panel(
        f"Starting ebook indexing\n"
        f"Directories: {', '.join(root_directories)}\n"
        f"Resume: {resume}\n"
        f"Job ID: {job_id or 'New job'}",
        title="Ebook Indexer",
        border_style="blue"
    ))

    try:
        # Initialize indexer
        indexer = EbookIndexer(config)

        # Run indexing with progress display
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
            TimeElapsedColumn(),
            console=console
        ) as progress:

            # Add progress task
            task = progress.add_task("Indexing files...", total=100)

            # Run indexing
            result_job_id = indexer.run_indexing(
                root_directories=root_directories,
                job_id=job_id,
                resume=resume
            )

            progress.update(task, completed=100)

        # Display results
        summary = indexer.get_indexing_summary(result_job_id)
        _display_indexing_results(summary)

        console.print(f"\n[green]Indexing completed successfully![/green]")
        console.print(f"Job ID: {result_job_id}")

    except IndexingError as e:
        console.print(f"[red]Indexing failed: {e}[/red]")
        sys.exit(1)
    except Exception as e:
        console.print(f"[red]Unexpected error: {e}[/red]")
        sys.exit(1)


@cli.command()
@click.option('--job-id', help='Specific job ID to retry')
@click.pass_context
def retry(ctx, job_id: Optional[str]):
    """Retry failed files and recheck anomalies."""
    config: AppConfig = ctx.obj['config']

    console.print(Panel(
        f"Retrying failed files\n"
        f"Job ID: {job_id or 'All recent failures'}",
        title="Retry Operation",
        border_style="yellow"
    ))

    try:
        indexer = EbookIndexer(config)

        with console.status("[bold green]Retrying failed files..."):
            results = indexer.retry_failed_files(job_id)

        # Display results
        table = Table(title="Retry Results")
        table.add_column("Metric", style="cyan")
        table.add_column("Count", style="magenta")

        table.add_row("Files Retried", str(results['retried']))
        table.add_row("Successful", str(results['successful']))
        table.add_row("Still Failed", str(results['still_failed']))

        console.print(table)

        if results['successful'] > 0:
            console.print(f"[green]{results['successful']} files successfully processed on retry[/green]")
        if results['still_failed'] > 0:
            console.print(f"[red]{results['still_failed']} files still failing[/red]")

    except Exception as e:
        console.print(f"[red]Retry failed: {e}[/red]")
        sys.exit(1)


@cli.command()
@click.option('--job-id', help='Specific job ID to show anomalies for')
@click.option('--severity', multiple=True, help='Filter by severity (low, medium, high)')
@click.option('--type', 'anomaly_types', multiple=True, help='Filter by anomaly type')
@click.option('--detailed', is_flag=True, help='Show detailed anomaly information')
@click.pass_context
def anomalies(ctx, job_id: Optional[str], severity: tuple, anomaly_types: tuple, detailed: bool):
    """Show anomalies from indexing jobs."""
    try:
        anomaly_repo = AnomalyRepository()

        if not job_id:
            # Get most recent job
            job_repo = JobRepository()
            recent_jobs = job_repo.get_recent_jobs(1)
            if not recent_jobs:
                console.print("[yellow]No jobs found[/yellow]")
                return
            job_id = recent_jobs[0].id

        # Get anomalies
        anomalies_data = anomaly_repo.get_anomalies_by_job(
            job_id,
            severity=list(severity) if severity else None,
            anomaly_types=list(anomaly_types) if anomaly_types else None
        )

        if not anomalies_data:
            console.print("[green]No anomalies found matching criteria[/green]")
            return

        # Display anomalies
        _display_anomalies(anomalies_data, detailed)

    except Exception as e:
        console.print(f"[red]Error retrieving anomalies: {e}[/red]")
        sys.exit(1)


@cli.command()
@click.option('--job-id', help='Specific job ID to show stats for')
@click.option('--limit', default=10, help='Number of recent jobs to show')
@click.pass_context
def stats(ctx, job_id: Optional[str], limit: int):
    """Show processing statistics."""
    try:
        job_repo = JobRepository()

        if job_id:
            # Show specific job stats
            job = job_repo.get_job_by_id(job_id)
            if not job:
                console.print(f"[red]Job {job_id} not found[/red]")
                return

            _display_job_stats(job)
        else:
            # Show recent jobs
            recent_jobs = job_repo.get_recent_jobs(limit)
            if not recent_jobs:
                console.print("[yellow]No jobs found[/yellow]")
                return

            _display_jobs_table(recent_jobs)

    except Exception as e:
        console.print(f"[red]Error retrieving statistics: {e}[/red]")
        sys.exit(1)


@cli.command()
@click.option('--format', 'output_format', default='json', type=click.Choice(['json', 'csv']),
              help='Output format')
@click.option('--output', '-o', help='Output file path')
@click.option('--job-id', help='Specific job ID to export')
@click.option('--include-anomalies', is_flag=True, help='Include anomaly information')
@click.pass_context
def export(ctx, output_format: str, output: Optional[str], job_id: Optional[str], include_anomalies: bool):
    """Export indexing results."""
    try:
        book_repo = BookRepository()

        # Get books
        if job_id:
            books = book_repo.get_books_by_job(job_id)
        else:
            books = book_repo.get_all_books()

        if not books:
            console.print("[yellow]No books found to export[/yellow]")
            return

        # Export data
        if output_format == 'json':
            data = [book.model_dump() for book in books]
            output_content = json.dumps(data, indent=2, default=str)
        else:  # CSV
            # Simplified CSV export
            import csv
            import io

            output_buffer = io.StringIO()
            writer = csv.writer(output_buffer)

            # Header
            headers = ['file_path', 'title', 'author', 'format', 'file_size', 'status']
            if include_anomalies:
                headers.append('anomaly_count')
            writer.writerow(headers)

            # Data rows
            for book in books:
                row = [
                    book.file_info.file_path,
                    book.metadata.title or '',
                    book.metadata.author or '',
                    book.metadata.format or '',
                    book.file_info.file_size,
                    book.processing.status.value
                ]
                if include_anomalies:
                    row.append(len(book.anomalies))
                writer.writerow(row)

            output_content = output_buffer.getvalue()

        # Write to file or stdout
        if output:
            with open(output, 'w') as f:
                f.write(output_content)
            console.print(f"[green]Exported {len(books)} books to {output}[/green]")
        else:
            console.print(output_content)

    except Exception as e:
        console.print(f"[red]Export failed: {e}[/red]")
        sys.exit(1)


def _display_indexing_results(summary: dict):
    """Display indexing results in a formatted table."""
    job_summary = summary['summary']

    table = Table(title="Indexing Results")
    table.add_column("Metric", style="cyan")
    table.add_column("Value", style="magenta")

    table.add_row("Total Files", str(job_summary['total_files']))
    table.add_row("Processed Files", str(job_summary['processed_files']))
    table.add_row("Successful Files", str(job_summary['successful_files']))
    table.add_row("Failed Files", str(job_summary['failed_files']))
    table.add_row("Files with Anomalies", str(job_summary['anomalous_files']))
    table.add_row("Processing Rate", f"{job_summary['processing_rate']:.2f} files/sec")
    table.add_row("Duration", f"{job_summary['duration_seconds']} seconds")
    table.add_row("Status", job_summary['status'])

    console.print(table)


def _display_anomalies(anomalies_data: list, detailed: bool):
    """Display anomalies in a formatted table."""
    if detailed:
        # Detailed view
        for anomaly in anomalies_data:
            panel_content = (
                f"File: {anomaly['file_path']}\n"
                f"Type: {anomaly['anomaly_type']}\n"
                f"Severity: {anomaly['severity']}\n"
                f"Description: {anomaly['description']}\n"
            )

            if anomaly.get('context'):
                context = anomaly['context']
                if context.get('expected_location'):
                    panel_content += f"Expected: {context['expected_location']}\n"
                if context.get('actual_location'):
                    panel_content += f"Actual: {context['actual_location']}\n"

            console.print(Panel(panel_content, title=f"Anomaly - {anomaly['severity'].upper()}",
                              border_style="red" if anomaly['severity'] == 'high' else "yellow"))
    else:
        # Summary table
        table = Table(title=f"Anomalies ({len(anomalies_data)} found)")
        table.add_column("File", style="cyan", max_width=50)
        table.add_column("Type", style="yellow")
        table.add_column("Severity", style="red")
        table.add_column("Description", style="white", max_width=60)

        for anomaly in anomalies_data:
            filename = Path(anomaly['file_path']).name
            table.add_row(
                filename,
                anomaly['anomaly_type'],
                anomaly['severity'],
                anomaly['description'][:60] + "..." if len(anomaly['description']) > 60 else anomaly['description']
            )

        console.print(table)


def _display_job_stats(job):
    """Display detailed statistics for a single job."""
    table = Table(title=f"Job Statistics - {job.id}")
    table.add_column("Metric", style="cyan")
    table.add_column("Value", style="magenta")

    # Basic info
    table.add_row("Job ID", str(job.id))
    table.add_row("Status", job.status.value)
    table.add_row("Root Directories", ", ".join(job.root_directories))

    # Statistics
    stats = job.statistics
    table.add_row("Total Files", str(stats.total_files))
    table.add_row("Processed Files", str(stats.processed_files))
    table.add_row("Successful Files", str(stats.successful_files))
    table.add_row("Failed Files", str(stats.failed_files))
    table.add_row("Files with Anomalies", str(stats.anomalous_files))
    table.add_row("Collections Found", str(stats.collections_found))
    table.add_row("Processing Rate", f"{stats.processing_rate_files_per_sec:.2f} files/sec")

    # Timing
    if job.timing.start_time:
        table.add_row("Start Time", job.timing.start_time.strftime("%Y-%m-%d %H:%M:%S"))
    if job.timing.end_time:
        table.add_row("End Time", job.timing.end_time.strftime("%Y-%m-%d %H:%M:%S"))
    if job.timing.duration_seconds:
        table.add_row("Duration", f"{job.timing.duration_seconds} seconds")

    # Anomaly summary
    anomaly_summary = job.anomaly_summary
    if anomaly_summary.total_anomalies > 0:
        table.add_row("Total Anomalies", str(anomaly_summary.total_anomalies))

        # Anomaly breakdown
        if anomaly_summary.by_severity:
            severity_breakdown = ", ".join([f"{k}: {v}" for k, v in anomaly_summary.by_severity.items()])
            table.add_row("By Severity", severity_breakdown)

        if anomaly_summary.by_type:
            type_breakdown = ", ".join([f"{k}: {v}" for k, v in anomaly_summary.by_type.items()])
            table.add_row("By Type", type_breakdown)

    console.print(table)


def _display_jobs_table(jobs):
    """Display a table of recent jobs."""
    table = Table(title="Recent Jobs")
    table.add_column("Job ID", style="cyan")
    table.add_column("Status", style="yellow")
    table.add_column("Files", style="magenta")
    table.add_column("Success Rate", style="green")
    table.add_column("Anomalies", style="red")
    table.add_column("Created", style="white")

    for job in jobs:
        stats = job.statistics
        success_rate = (stats.successful_files / stats.total_files * 100) if stats.total_files > 0 else 0

        table.add_row(
            str(job.id)[:8] + "...",  # Truncate ID
            job.status.value,
            f"{stats.processed_files}/{stats.total_files}",
            f"{success_rate:.1f}%",
            str(job.anomaly_summary.total_anomalies),
            job.created_date.strftime("%Y-%m-%d %H:%M")
        )

    console.print(table)


if __name__ == "__main__":
    cli()
