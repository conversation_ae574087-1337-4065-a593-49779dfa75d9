"""Tests for file utilities."""

import os
import tempfile
import pytest
from pathlib import Path
from datetime import datetime

from ebook_indexer.utils.file_utils import FileUtils
from ebook_indexer.exceptions.custom_exceptions import FileProcessingError


class TestFileUtils:
    """Test file utilities."""
    
    def test_calculate_file_hash(self):
        """Test file hash calculation."""
        with tempfile.NamedTemporaryFile(mode='w', delete=False) as f:
            f.write("Hello, World!")
            f.flush()
            
            try:
                # Test SHA256 hash
                hash_value = FileUtils.calculate_file_hash(f.name, 'sha256')
                expected = "dffd6021bb2bd5b0af676290809ec3a53191dd81c7f70a4b28688a362182986f"
                assert hash_value == expected
                
                # Test MD5 hash
                hash_value = FileUtils.calculate_file_hash(f.name, 'md5')
                expected = "65a8e27d8879283831b664bd8b7f0ad4"
                assert hash_value == expected
                
            finally:
                os.unlink(f.name)
    
    def test_calculate_file_hash_nonexistent(self):
        """Test hash calculation for nonexistent file."""
        with pytest.raises(FileProcessingError) as exc_info:
            FileUtils.calculate_file_hash("/nonexistent/file.txt")
        
        assert "File not found" in str(exc_info.value)
    
    def test_get_file_size(self):
        """Test file size calculation."""
        with tempfile.NamedTemporaryFile(mode='w', delete=False) as f:
            content = "Hello, World!" * 100
            f.write(content)
            f.flush()
            
            try:
                size = FileUtils.get_file_size(f.name)
                expected_size = len(content.encode('utf-8'))
                assert size == expected_size
                
            finally:
                os.unlink(f.name)
    
    def test_get_file_size_nonexistent(self):
        """Test file size for nonexistent file."""
        with pytest.raises(FileProcessingError) as exc_info:
            FileUtils.get_file_size("/nonexistent/file.txt")
        
        assert "File not found" in str(exc_info.value)
    
    def test_get_file_modified_time(self):
        """Test file modified time."""
        with tempfile.NamedTemporaryFile(mode='w', delete=False) as f:
            f.write("test content")
            f.flush()
            
            try:
                modified_time = FileUtils.get_file_modified_time(f.name)
                assert isinstance(modified_time, datetime)
                
                # Should be recent (within last minute)
                now = datetime.now()
                time_diff = (now - modified_time).total_seconds()
                assert time_diff < 60
                
            finally:
                os.unlink(f.name)
    
    def test_detect_file_type(self):
        """Test file type detection."""
        with tempfile.NamedTemporaryFile(suffix='.txt', mode='w', delete=False) as f:
            f.write("This is a text file")
            f.flush()
            
            try:
                mime_type = FileUtils.detect_file_type(f.name)
                # Should detect as text file
                assert 'text' in mime_type.lower()
                
            finally:
                os.unlink(f.name)
    
    def test_is_supported_ebook(self):
        """Test ebook format detection."""
        # Test supported formats
        assert FileUtils.is_supported_ebook("book.pdf") is True
        assert FileUtils.is_supported_ebook("book.epub") is True
        assert FileUtils.is_supported_ebook("book.mobi") is True
        assert FileUtils.is_supported_ebook("BOOK.PDF") is True  # Case insensitive
        
        # Test unsupported formats
        assert FileUtils.is_supported_ebook("document.docx") is False
        assert FileUtils.is_supported_ebook("image.jpg") is False
        assert FileUtils.is_supported_ebook("archive.zip") is False
    
    def test_get_file_info(self):
        """Test comprehensive file info extraction."""
        with tempfile.NamedTemporaryFile(suffix='.pdf', mode='w', delete=False) as f:
            f.write("Fake PDF content")
            f.flush()
            
            try:
                file_info = FileUtils.get_file_info(f.name)
                
                # Check all expected fields are present
                expected_fields = [
                    'file_path', 'directory_path', 'filename', 'file_extension',
                    'file_size', 'file_hash', 'last_modified', 'mime_type', 'is_supported'
                ]
                
                for field in expected_fields:
                    assert field in file_info
                
                # Check specific values
                assert file_info['filename'] == Path(f.name).name
                assert file_info['file_extension'] == '.pdf'
                assert file_info['is_supported'] is True
                assert file_info['file_size'] > 0
                assert isinstance(file_info['last_modified'], datetime)
                
            finally:
                os.unlink(f.name)
    
    def test_get_file_info_nonexistent(self):
        """Test file info for nonexistent file."""
        with pytest.raises(FileProcessingError) as exc_info:
            FileUtils.get_file_info("/nonexistent/file.pdf")
        
        assert "File does not exist" in str(exc_info.value)
    
    def test_format_file_size(self):
        """Test file size formatting."""
        assert FileUtils.format_file_size(0) == "0 B"
        assert FileUtils.format_file_size(512) == "512.0 B"
        assert FileUtils.format_file_size(1024) == "1.0 KB"
        assert FileUtils.format_file_size(1536) == "1.5 KB"
        assert FileUtils.format_file_size(1024 * 1024) == "1.0 MB"
        assert FileUtils.format_file_size(1024 * 1024 * 1024) == "1.0 GB"
    
    def test_safe_filename(self):
        """Test safe filename generation."""
        # Test problematic characters
        assert FileUtils.safe_filename("file<name>.pdf") == "file_name_.pdf"
        assert FileUtils.safe_filename("file:name.pdf") == "file_name.pdf"
        assert FileUtils.safe_filename("file/name.pdf") == "file_name.pdf"
        
        # Test leading/trailing dots and spaces
        assert FileUtils.safe_filename("  .filename.  ") == "filename."
        assert FileUtils.safe_filename("...") == "unnamed_file"
        assert FileUtils.safe_filename("") == "unnamed_file"
        
        # Test normal filename
        assert FileUtils.safe_filename("normal_file.pdf") == "normal_file.pdf"
    
    def test_find_files_by_extension(self):
        """Test finding files by extension."""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create test files
            test_files = [
                "book1.pdf",
                "book2.epub", 
                "document.txt",
                "image.jpg"
            ]
            
            for filename in test_files:
                file_path = Path(temp_dir) / filename
                file_path.write_text("test content")
            
            # Create subdirectory with more files
            sub_dir = Path(temp_dir) / "subdir"
            sub_dir.mkdir()
            (sub_dir / "book3.pdf").write_text("test content")
            
            # Test finding PDF files recursively
            pdf_files = FileUtils.find_files_by_extension(temp_dir, ['.pdf'], recursive=True)
            assert len(pdf_files) == 2
            assert any("book1.pdf" in f for f in pdf_files)
            assert any("book3.pdf" in f for f in pdf_files)
            
            # Test finding PDF files non-recursively
            pdf_files = FileUtils.find_files_by_extension(temp_dir, ['.pdf'], recursive=False)
            assert len(pdf_files) == 1
            assert any("book1.pdf" in f for f in pdf_files)
            
            # Test multiple extensions
            ebook_files = FileUtils.find_files_by_extension(temp_dir, ['.pdf', '.epub'], recursive=True)
            assert len(ebook_files) == 3
    
    def test_find_files_nonexistent_directory(self):
        """Test finding files in nonexistent directory."""
        with pytest.raises(FileProcessingError) as exc_info:
            FileUtils.find_files_by_extension("/nonexistent/directory", ['.pdf'])
        
        assert "Directory does not exist" in str(exc_info.value)
    
    def test_validate_path(self):
        """Test path validation."""
        with tempfile.NamedTemporaryFile() as f:
            # Test valid file path
            assert FileUtils.validate_path(f.name) is True
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # Test valid directory path
            assert FileUtils.validate_path(temp_dir) is True
        
        # Test invalid path
        assert FileUtils.validate_path("/nonexistent/path") is False
        assert FileUtils.validate_path("") is False
