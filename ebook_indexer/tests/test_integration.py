"""Integration tests for the complete ebook indexer system."""

import pytest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch
from datetime import datetime

from ebook_indexer.config.settings import AppConfig
from ebook_indexer.core.indexer import EbookIndexer
from ebook_indexer.core.anomaly_detector import AnomalyDetector
from ebook_indexer.core.scanner import DirectoryScanner, FileDiscovery
from ebook_indexer.core.metadata_extractor import MetadataExtractor
from ebook_indexer.database.models import (
    AnomalyType, AnomalySeverity, ProcessingStatus, JobStatus
)
from ebook_indexer.utils.file_utils import FileUtils
from ebook_indexer.utils.progress_tracker import ProgressTracker


class TestAnomalyDetector:
    """Test anomaly detection functionality."""
    
    def setup_method(self):
        """Setup test environment."""
        self.config = {
            'max_nesting_depth': 4,
            'enforce_naming_convention': True,
            'detect_misplaced_files': True,
            'severity_thresholds': {
                'wrong_level': 'medium',
                'deep_nesting': 'low',
                'missing_directory': 'high',
                'naming_violation': 'low'
            }
        }
        self.detector = AnomalyDetector(self.config)
    
    def test_detect_misplaced_file(self):
        """Test detection of misplaced files."""
        from ebook_indexer.database.models import FileInfo, StructureInfo
        
        # Create file info for a file directly in collection root (level 2)
        file_info = FileInfo(
            file_path="/root/collection/book.pdf",
            directory_path="/root/collection",
            filename="book.pdf",
            file_extension=".pdf",
            file_size=1024,
            file_hash="test123",
            last_modified=datetime.utcnow()
        )
        
        structure_info = StructureInfo(
            expected_path="/root/collection/book-dir/book.pdf",
            collection_name="collection",
            book_directory=None,
            nesting_level=2,  # Should be 3
            follows_convention=False
        )
        
        anomalies = self.detector.detect_anomalies(file_info, structure_info)
        
        assert len(anomalies) >= 1
        misplaced_anomalies = [a for a in anomalies if a.type == AnomalyType.MISPLACED_FILE]
        assert len(misplaced_anomalies) == 1
        assert misplaced_anomalies[0].severity == AnomalySeverity.MEDIUM
    
    def test_detect_deep_nesting(self):
        """Test detection of deep nesting."""
        from ebook_indexer.database.models import FileInfo, StructureInfo
        
        file_info = FileInfo(
            file_path="/root/collection/book/subdir/deep/book.pdf",
            directory_path="/root/collection/book/subdir/deep",
            filename="book.pdf",
            file_extension=".pdf",
            file_size=1024,
            file_hash="test123",
            last_modified=datetime.utcnow()
        )
        
        structure_info = StructureInfo(
            expected_path="/root/collection/book/subdir/deep/book.pdf",
            collection_name="collection",
            book_directory="book",
            nesting_level=5,  # Exceeds max of 4
            follows_convention=False
        )
        
        anomalies = self.detector.detect_anomalies(file_info, structure_info)
        
        deep_nesting_anomalies = [a for a in anomalies if a.type == AnomalyType.DEEP_NESTING]
        assert len(deep_nesting_anomalies) == 1
        assert deep_nesting_anomalies[0].severity == AnomalySeverity.LOW
    
    def test_detect_naming_violations(self):
        """Test detection of naming convention violations."""
        from ebook_indexer.database.models import FileInfo, StructureInfo
        
        file_info = FileInfo(
            file_path="/root/collection/book/bad<>file.pdf",
            directory_path="/root/collection/book",
            filename="bad<>file.pdf",  # Contains problematic characters
            file_extension=".pdf",
            file_size=1024,
            file_hash="test123",
            last_modified=datetime.utcnow()
        )
        
        structure_info = StructureInfo(
            expected_path="/root/collection/book/bad<>file.pdf",
            collection_name="collection",
            book_directory="book",
            nesting_level=3,
            follows_convention=False
        )
        
        anomalies = self.detector.detect_anomalies(file_info, structure_info)
        
        naming_anomalies = [a for a in anomalies if a.type == AnomalyType.NAMING_CONVENTION]
        assert len(naming_anomalies) >= 1
        assert all(a.severity == AnomalySeverity.LOW for a in naming_anomalies)


class TestProgressTracker:
    """Test progress tracking functionality."""
    
    def test_basic_progress_tracking(self):
        """Test basic progress tracking operations."""
        tracker = ProgressTracker(total_items=100, description="Test Operation")
        
        # Test initial state
        assert not tracker.is_started
        assert not tracker.is_finished
        assert tracker.stats.total_items == 100
        assert tracker.stats.processed_items == 0
        
        # Test starting
        tracker.start()
        assert tracker.is_started
        assert tracker.stats.start_time is not None
        
        # Test updates
        tracker.update(successful_delta=10)
        assert tracker.stats.processed_items == 10
        assert tracker.stats.successful_items == 10
        assert tracker.stats.completion_percentage == 10.0
        
        # Test finishing
        tracker.update(successful_delta=90)  # Complete remaining
        assert tracker.is_finished
        assert tracker.stats.completion_percentage == 100.0
    
    def test_progress_calculations(self):
        """Test progress calculation methods."""
        tracker = ProgressTracker(total_items=50)
        tracker.start()
        
        # Process some items
        tracker.update(successful_delta=25)
        
        stats = tracker.get_stats()
        assert stats.completion_percentage == 50.0
        assert stats.processing_rate > 0  # Should have some rate
        assert stats.estimated_time_remaining is not None


class TestFileUtils:
    """Test file utility functions."""
    
    def setup_method(self):
        """Setup test environment with temporary files."""
        self.temp_dir = tempfile.mkdtemp()
        self.temp_path = Path(self.temp_dir)
        
        # Create test files
        self.test_file = self.temp_path / "test.pdf"
        self.test_file.write_text("Test PDF content")
        
        self.test_dir = self.temp_path / "subdir"
        self.test_dir.mkdir()
        (self.test_dir / "nested.epub").write_text("Test EPUB content")
    
    def teardown_method(self):
        """Clean up temporary files."""
        shutil.rmtree(self.temp_dir)
    
    def test_file_hash_calculation(self):
        """Test file hash calculation."""
        hash_value = FileUtils.calculate_file_hash(str(self.test_file))
        assert len(hash_value) == 64  # SHA256 hex length
        assert hash_value.isalnum()
        
        # Same file should produce same hash
        hash_value2 = FileUtils.calculate_file_hash(str(self.test_file))
        assert hash_value == hash_value2
    
    def test_file_size_calculation(self):
        """Test file size calculation."""
        size = FileUtils.get_file_size(str(self.test_file))
        assert size > 0
        assert size == len("Test PDF content")
    
    def test_find_files_by_extension(self):
        """Test finding files by extension."""
        files = FileUtils.find_files_by_extension(
            self.temp_dir, 
            ['.pdf', '.epub'], 
            recursive=True
        )
        
        assert len(files) == 2
        assert any('test.pdf' in f for f in files)
        assert any('nested.epub' in f for f in files)
    
    def test_file_info_extraction(self):
        """Test comprehensive file info extraction."""
        info = FileUtils.get_file_info(str(self.test_file))
        
        assert info['filename'] == 'test.pdf'
        assert info['file_extension'] == '.pdf'
        assert info['file_size'] > 0
        assert info['file_hash']
        assert info['is_supported'] is True
        assert 'last_modified' in info


class TestDirectoryScanner:
    """Test directory scanning functionality."""
    
    def setup_method(self):
        """Setup test directory structure."""
        self.temp_dir = tempfile.mkdtemp()
        self.temp_path = Path(self.temp_dir)
        
        # Create test directory structure
        # root/
        #   collection1/
        #     book1/
        #       book1.pdf
        #     book2/
        #       book2.epub
        #   collection2/
        #     book3.pdf (misplaced - should be in subdirectory)
        
        collection1 = self.temp_path / "collection1"
        collection1.mkdir()
        
        book1_dir = collection1 / "book1"
        book1_dir.mkdir()
        (book1_dir / "book1.pdf").write_text("PDF content")
        
        book2_dir = collection1 / "book2"
        book2_dir.mkdir()
        (book2_dir / "book2.epub").write_text("EPUB content")
        
        collection2 = self.temp_path / "collection2"
        collection2.mkdir()
        (collection2 / "book3.pdf").write_text("Misplaced PDF")
        
        self.scanner = DirectoryScanner(['.pdf', '.epub'])
    
    def teardown_method(self):
        """Clean up temporary files."""
        shutil.rmtree(self.temp_dir)
    
    def test_directory_scanning(self):
        """Test basic directory scanning."""
        discoveries = list(self.scanner.scan_directory(str(self.temp_path)))
        
        # Should find 3 files
        assert len(discoveries) == 3
        
        # Check file types
        pdf_files = [d for d in discoveries if d.file_extension == '.pdf']
        epub_files = [d for d in discoveries if d.file_extension == '.epub']
        
        assert len(pdf_files) == 2
        assert len(epub_files) == 1
    
    def test_structure_analysis(self):
        """Test directory structure analysis."""
        discoveries = list(self.scanner.scan_directory(str(self.temp_path)))
        
        # Find the misplaced file
        misplaced = [d for d in discoveries if d.filename == 'book3.pdf'][0]
        assert misplaced.nesting_level == 2  # Should be 3 for proper structure
        assert misplaced.collection_name == 'collection2'
        assert misplaced.book_directory is None  # No book directory
        
        # Find properly placed file
        proper = [d for d in discoveries if d.filename == 'book1.pdf'][0]
        assert proper.nesting_level == 3
        assert proper.collection_name == 'collection1'
        assert proper.book_directory == 'book1'
    
    def test_scan_summary(self):
        """Test scan summary generation."""
        summary = self.scanner.get_scan_summary([str(self.temp_path)])
        
        assert summary.total_files == 3
        assert summary.supported_files == 3  # All are PDF/EPUB
        assert summary.unsupported_files == 0
        assert summary.directories_scanned > 0
        assert summary.scan_time_seconds >= 0


class TestMetadataExtractor:
    """Test metadata extraction functionality."""
    
    def setup_method(self):
        """Setup test environment."""
        self.extractor = MetadataExtractor()
        self.temp_dir = tempfile.mkdtemp()
        self.temp_path = Path(self.temp_dir)
    
    def teardown_method(self):
        """Clean up temporary files."""
        shutil.rmtree(self.temp_dir)
    
    def test_basic_metadata_extraction(self):
        """Test basic metadata extraction for unsupported formats."""
        test_file = self.temp_path / "test_book.txt"
        test_file.write_text("Test content")
        
        metadata = self.extractor.extract_metadata(str(test_file))
        
        assert metadata['title'] == 'Test Book'  # Cleaned from filename
        assert metadata['format'] == 'TXT'
        assert metadata['author'] is None
        assert metadata['publisher'] is None
    
    def test_title_extraction_from_filename(self):
        """Test title extraction from various filename patterns."""
        test_cases = [
            ("The_Great_Book.pdf", "The Great Book"),
            ("author-name_book-title[2023].epub", "Author Name Book Title"),
            ("book.with.dots.pdf", "Book With Dots"),
            ("BOOK_IN_CAPS.pdf", "Book In Caps")
        ]
        
        for filename, expected_title in test_cases:
            test_file = self.temp_path / filename
            test_file.write_text("Test content")
            
            metadata = self.extractor.extract_metadata(str(test_file))
            assert metadata['title'] == expected_title
            
            # Clean up
            test_file.unlink()


@pytest.mark.integration
class TestEbookIndexerIntegration:
    """Integration tests for the complete indexer system."""
    
    def setup_method(self):
        """Setup test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.temp_path = Path(self.temp_dir)
        
        # Create test configuration
        self.config = AppConfig(
            root_directories=[str(self.temp_path)],
            supported_extensions=['.pdf', '.epub', '.txt'],
            max_workers=1,
            batch_size=10,
            anomaly_detection={
                'max_nesting_depth': 4,
                'enforce_naming_convention': True,
                'detect_misplaced_files': True,
                'severity_thresholds': {
                    'wrong_level': 'medium',
                    'deep_nesting': 'low',
                    'missing_directory': 'high'
                }
            }
        )
        
        # Create test directory structure with various scenarios
        self._create_test_structure()
    
    def teardown_method(self):
        """Clean up temporary files."""
        shutil.rmtree(self.temp_dir)
    
    def _create_test_structure(self):
        """Create test directory structure with various scenarios."""
        # Good structure: collection/book-dir/file
        good_collection = self.temp_path / "good-collection"
        good_collection.mkdir()
        
        book1_dir = good_collection / "book-1"
        book1_dir.mkdir()
        (book1_dir / "book1.pdf").write_text("Good PDF content")
        
        book2_dir = good_collection / "book-2"
        book2_dir.mkdir()
        (book2_dir / "book2.epub").write_text("Good EPUB content")
        
        # Bad structure: files directly in collection
        bad_collection = self.temp_path / "bad-collection"
        bad_collection.mkdir()
        (bad_collection / "misplaced.pdf").write_text("Misplaced PDF")
        
        # Deep nesting
        deep_collection = self.temp_path / "deep-collection"
        deep_collection.mkdir()
        deep_path = deep_collection / "book" / "sub1" / "sub2" / "sub3"
        deep_path.mkdir(parents=True)
        (deep_path / "deep.txt").write_text("Deeply nested file")
    
    @patch('ebook_indexer.database.repository.get_connection')
    def test_complete_indexing_workflow(self, mock_get_connection):
        """Test complete indexing workflow with mocked database."""
        # Mock database connection and repositories
        mock_connection = Mock()
        mock_get_connection.return_value = mock_connection
        
        with patch('ebook_indexer.core.indexer.BookRepository') as mock_book_repo, \
             patch('ebook_indexer.core.indexer.JobRepository') as mock_job_repo, \
             patch('ebook_indexer.core.indexer.AnomalyRepository') as mock_anomaly_repo:
            
            # Setup mock repositories
            mock_book_repo_instance = Mock()
            mock_job_repo_instance = Mock()
            mock_anomaly_repo_instance = Mock()
            
            mock_book_repo.return_value = mock_book_repo_instance
            mock_job_repo.return_value = mock_job_repo_instance
            mock_anomaly_repo.return_value = mock_anomaly_repo_instance
            
            # Mock job creation
            mock_job_repo_instance.save_job.return_value = "test-job-id"
            
            # Create indexer and run
            indexer = EbookIndexer(self.config)
            
            # This should complete without errors
            job_id = indexer.run_indexing()
            
            assert job_id == "test-job-id"
            
            # Verify repositories were called
            assert mock_book_repo_instance.save_book.called
            assert mock_job_repo_instance.save_job.called
