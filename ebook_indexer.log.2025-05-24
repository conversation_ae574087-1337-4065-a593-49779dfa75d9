2025-05-24 23:53:52 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-24 23:53:52 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-24 23:54:00 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-24 23:54:00 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-24 23:54:16 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-24 23:54:16 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-24 23:54:16 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanner initialized with extensions: ['.pdf', '.epub', '.mobi', '.azw', '.azw3']
2025-05-24 23:54:16 - ebook_indexer.core.metadata_extractor.MetadataExtractor - INFO - Metadata extractor initialized for formats: ['.pdf', '.epub']
2025-05-24 23:54:16 - ebook_indexer.core.anomaly_detector.AnomalyDetector - INFO - Anomaly detector initialized with config: {'max_nesting_depth': 4, 'enforce_naming_convention': True, 'detect_misplaced_files': True, 'severity_thresholds': {'wrong_level': 'medium', 'deep_nesting': 'low', 'missing_directory': 'high', 'naming_violation': 'low'}}
2025-05-24 23:54:16 - ebook_indexer.core.indexer.EbookIndexer - INFO - Ebook indexer initialized
2025-05-24 23:54:16 - ebook_indexer.database.repository.JobRepository - ERROR - MongoDB save_job failed: Job with ID 68324008ae0009319782ee86 not found for update
2025-05-24 23:55:57 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-24 23:55:57 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-24 23:55:57 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanner initialized with extensions: ['.pdf', '.epub', '.mobi', '.azw', '.azw3']
2025-05-24 23:55:57 - ebook_indexer.core.metadata_extractor.MetadataExtractor - INFO - Metadata extractor initialized for formats: ['.pdf', '.epub']
2025-05-24 23:55:57 - ebook_indexer.core.anomaly_detector.AnomalyDetector - INFO - Anomaly detector initialized with config: {'max_nesting_depth': 4, 'enforce_naming_convention': True, 'detect_misplaced_files': True, 'severity_thresholds': {'wrong_level': 'medium', 'deep_nesting': 'low', 'missing_directory': 'high', 'naming_violation': 'low'}}
2025-05-24 23:55:57 - ebook_indexer.core.indexer.EbookIndexer - INFO - Ebook indexer initialized
2025-05-24 23:55:57 - ebook_indexer.database.repository.JobRepository - ERROR - MongoDB save_job failed: Job with ID 6832406ddd985cba48d075aa not found for update
2025-05-24 23:56:53 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-24 23:56:53 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-24 23:56:53 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanner initialized with extensions: ['.pdf', '.epub', '.mobi', '.azw', '.azw3']
2025-05-24 23:56:53 - ebook_indexer.core.metadata_extractor.MetadataExtractor - INFO - Metadata extractor initialized for formats: ['.pdf', '.epub']
2025-05-24 23:56:53 - ebook_indexer.core.anomaly_detector.AnomalyDetector - INFO - Anomaly detector initialized with config: {'max_nesting_depth': 4, 'enforce_naming_convention': True, 'detect_misplaced_files': True, 'severity_thresholds': {'wrong_level': 'medium', 'deep_nesting': 'low', 'missing_directory': 'high', 'naming_violation': 'low'}}
2025-05-24 23:56:53 - ebook_indexer.core.indexer.EbookIndexer - INFO - Ebook indexer initialized
2025-05-24 23:56:53 - ebook_indexer.database.repository.JobRepository - ERROR - MongoDB save_job failed: Job with ID 683240a598f8951f0826ec28 not found for update
2025-05-24 23:58:07 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-24 23:58:07 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-24 23:58:07 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanner initialized with extensions: ['.pdf', '.epub', '.mobi', '.azw', '.azw3']
2025-05-24 23:58:07 - ebook_indexer.core.metadata_extractor.MetadataExtractor - INFO - Metadata extractor initialized for formats: ['.pdf', '.epub']
2025-05-24 23:58:07 - ebook_indexer.core.anomaly_detector.AnomalyDetector - INFO - Anomaly detector initialized with config: {'max_nesting_depth': 4, 'enforce_naming_convention': True, 'detect_misplaced_files': True, 'severity_thresholds': {'wrong_level': 'medium', 'deep_nesting': 'low', 'missing_directory': 'high', 'naming_violation': 'low'}}
2025-05-24 23:58:07 - ebook_indexer.core.indexer.EbookIndexer - INFO - Ebook indexer initialized
2025-05-24 23:58:07 - ebook_indexer.database.repository.JobRepository - ERROR - MongoDB get_job_by_id failed: 'None' is not a valid ObjectId, it must be a 12-byte input or a 24-character hex string
2025-05-24 23:59:24 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-24 23:59:24 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-24 23:59:24 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanner initialized with extensions: ['.pdf', '.epub', '.mobi', '.azw', '.azw3']
2025-05-24 23:59:24 - ebook_indexer.core.metadata_extractor.MetadataExtractor - INFO - Metadata extractor initialized for formats: ['.pdf', '.epub']
2025-05-24 23:59:24 - ebook_indexer.core.anomaly_detector.AnomalyDetector - INFO - Anomaly detector initialized with config: {'max_nesting_depth': 4, 'enforce_naming_convention': True, 'detect_misplaced_files': True, 'severity_thresholds': {'wrong_level': 'medium', 'deep_nesting': 'low', 'missing_directory': 'high', 'naming_violation': 'low'}}
2025-05-24 23:59:24 - ebook_indexer.core.indexer.EbookIndexer - INFO - Ebook indexer initialized
2025-05-24 23:59:24 - ebook_indexer.core.indexer.EbookIndexer - INFO - Created new job 6832413c6fe7749dffb86a0d
2025-05-24 23:59:24 - ebook_indexer.core.indexer.EbookIndexer - INFO - Starting indexing job 6832413c6fe7749dffb86a0d for directories: ['test_ebooks']
2025-05-24 23:59:24 - ebook_indexer.core.indexer.EbookIndexer - INFO - Found 0 files to process
2025-05-24 23:59:24 - ebook_indexer.utils.progress_tracker.ProgressTracker - INFO - Progress tracker initialized: Indexing 1 directories (0 items)
2025-05-24 23:59:24 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanning directory: test_ebooks
2025-05-24 23:59:24 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Starting scan of directory: /home/<USER>/Projects-Python/Directory-Analyzer-Agent/test_ebooks
2025-05-24 23:59:24 - ebook_indexer.database.repository.BookRepository - ERROR - MongoDB save_book failed: Book with ID 6832413c6fe7749dffb86a0e not found for update
2025-05-24 23:59:24 - ebook_indexer.core.indexer.EbookIndexer - ERROR - Failed to process file /home/<USER>/Projects-Python/Directory-Analyzer-Agent/test_ebooks/fiction/great-gatsby/gatsby.txt: MongoDB save_book failed: Book with ID 6832413c6fe7749dffb86a0e not found for update
2025-05-24 23:59:24 - ebook_indexer.database.repository.BookRepository - ERROR - MongoDB save_book failed: Book with ID 6832413c6fe7749dffb86a0f not found for update
2025-05-24 23:59:24 - ebook_indexer.utils.progress_tracker.ProgressTracker - INFO - Completed: Indexing 1 directories - 0/0 items (0.0%) in 0:00:00 (0.00 items/sec)
2025-05-24 23:59:24 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories: 0/0 (0.0%)
2025-05-24 23:59:24 - ebook_indexer.core.indexer.EbookIndexer - ERROR - Job 6832413c6fe7749dffb86a0d failed: MongoDB save_book failed: Book with ID 6832413c6fe7749dffb86a0f not found for update
