[project]
name = "ebook-indexer"
version = "0.1.0"
description = "A comprehensive ebook directory analysis and indexing tool with anomaly detection"
requires-python = ">=3.12"
dependencies = [
    # Core dependencies
    "pymongo>=4.0.0",        # MongoDB driver
    "pydantic>=1.10.0",      # Data validation and models
    "click>=8.0.0",          # CLI framework
    "rich>=12.0.0",          # Rich console output
    "pyyaml>=6.0.0",         # YAML configuration support

    # Metadata extraction
    "PyPDF2>=2.0.0",         # PDF metadata
    "ebooklib>=0.18",        # EPUB metadata
    # Note: MOBI support will be added later with alternative library

    # Utilities
    "python-magic>=0.4.0",   # File type detection
    "xxhash>=3.0.0",         # Fast file hashing
    "tqdm>=4.64.0",          # Progress bars
]

[project.optional-dependencies]
dev = [
    "pytest>=6.0.0",
    "black>=22.0.0",
    "flake8>=4.0.0",
]

[project.scripts]
ebook-indexer = "ebook_indexer.main:cli"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.pytest.ini_options]
testpaths = ["ebook_indexer/tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]

[tool.black]
line-length = 100
target-version = ['py312']

[tool.flake8]
max-line-length = 100
extend-ignore = ["E203", "W503"]

[dependency-groups]
dev = [
    "pytest>=8.3.5",
]
