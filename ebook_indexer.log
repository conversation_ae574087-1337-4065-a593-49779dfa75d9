2025-05-25 00:00:36 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-25 00:00:36 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-25 00:00:36 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanner initialized with extensions: ['.pdf', '.epub', '.mobi', '.azw', '.azw3']
2025-05-25 00:00:36 - ebook_indexer.core.metadata_extractor.MetadataExtractor - INFO - Metadata extractor initialized for formats: ['.pdf', '.epub']
2025-05-25 00:00:36 - ebook_indexer.core.anomaly_detector.AnomalyDetector - INFO - Anomaly detector initialized with config: {'max_nesting_depth': 4, 'enforce_naming_convention': True, 'detect_misplaced_files': True, 'severity_thresholds': {'wrong_level': 'medium', 'deep_nesting': 'low', 'missing_directory': 'high', 'naming_violation': 'low'}}
2025-05-25 00:00:36 - ebook_indexer.core.indexer.EbookIndexer - INFO - Ebook indexer initialized
2025-05-25 00:00:36 - ebook_indexer.core.indexer.EbookIndexer - INFO - Created new job 6832418411d05ca4cc376e83
2025-05-25 00:00:36 - ebook_indexer.core.indexer.EbookIndexer - INFO - Starting indexing job 6832418411d05ca4cc376e83 for directories: ['test_ebooks']
2025-05-25 00:00:36 - ebook_indexer.core.indexer.EbookIndexer - INFO - Found 0 files to process
2025-05-25 00:00:36 - ebook_indexer.utils.progress_tracker.ProgressTracker - INFO - Progress tracker initialized: Indexing 1 directories (0 items)
2025-05-25 00:00:36 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanning directory: test_ebooks
2025-05-25 00:00:36 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Starting scan of directory: /home/<USER>/Projects-Python/Directory-Analyzer-Agent/test_ebooks
2025-05-25 00:00:36 - ebook_indexer.utils.progress_tracker.ProgressTracker - INFO - Started: Indexing 1 directories
2025-05-25 00:00:36 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories: 0/0 (0.0%)
2025-05-25 00:00:36 - ebook_indexer.utils.progress_tracker.ProgressTracker - INFO - Completed: Indexing 1 directories - 1/0 items (0.0%) in 0:00:00.000075 (13333.33 items/sec)
2025-05-25 00:00:36 - ebook_indexer.core.indexer.EbookIndexer - INFO - Job 6832418411d05ca4cc376e83 completed: 1/1 files successful, 0 failed, 1 with anomalies
2025-05-25 00:01:23 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-25 00:01:23 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-25 00:01:23 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanner initialized with extensions: ['.pdf', '.epub', '.mobi', '.azw', '.azw3', '.txt']
2025-05-25 00:01:23 - ebook_indexer.core.metadata_extractor.MetadataExtractor - INFO - Metadata extractor initialized for formats: ['.pdf', '.epub']
2025-05-25 00:01:23 - ebook_indexer.core.anomaly_detector.AnomalyDetector - INFO - Anomaly detector initialized with config: {'max_nesting_depth': 4, 'enforce_naming_convention': True, 'detect_misplaced_files': True, 'severity_thresholds': {'wrong_level': 'medium', 'deep_nesting': 'low', 'missing_directory': 'high', 'naming_violation': 'low'}}
2025-05-25 00:01:23 - ebook_indexer.core.indexer.EbookIndexer - INFO - Ebook indexer initialized
2025-05-25 00:01:23 - ebook_indexer.core.indexer.EbookIndexer - INFO - Created new job 683241b3e7a80e77ac1c19f4
2025-05-25 00:01:23 - ebook_indexer.core.indexer.EbookIndexer - INFO - Starting indexing job 683241b3e7a80e77ac1c19f4 for directories: ['test_ebooks']
2025-05-25 00:01:23 - ebook_indexer.core.indexer.EbookIndexer - INFO - Found 1 files to process
2025-05-25 00:01:23 - ebook_indexer.utils.progress_tracker.ProgressTracker - INFO - Progress tracker initialized: Indexing 1 directories (1 items)
2025-05-25 00:01:23 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanning directory: test_ebooks
2025-05-25 00:01:23 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Starting scan of directory: /home/<USER>/Projects-Python/Directory-Analyzer-Agent/test_ebooks
2025-05-25 00:01:23 - ebook_indexer.utils.progress_tracker.ProgressTracker - INFO - Started: Indexing 1 directories
2025-05-25 00:01:23 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories: 0/1 (0.0%)
2025-05-25 00:01:23 - ebook_indexer.utils.progress_tracker.ProgressTracker - INFO - Completed: Indexing 1 directories - 1/1 items (100.0%) in 0:00:00.000072 (13888.89 items/sec)
2025-05-25 00:01:23 - ebook_indexer.core.indexer.EbookIndexer - INFO - Job 683241b3e7a80e77ac1c19f4 completed: 1/1 files successful, 0 failed, 1 with anomalies
2025-05-25 00:01:44 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-25 00:01:44 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-25 00:01:55 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-25 00:01:55 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-25 00:03:00 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-25 00:03:00 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-25 00:03:43 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-25 00:03:43 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-25 00:03:54 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-25 00:03:54 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-25 00:04:13 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-25 00:04:13 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-25 00:05:14 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-25 00:05:14 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-25 00:05:24 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-25 00:05:24 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-25 00:06:46 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-25 00:06:46 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-25 00:08:25 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-25 00:08:25 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
